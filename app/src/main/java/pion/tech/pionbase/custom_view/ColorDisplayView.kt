package pion.tech.pionbase.custom_view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import androidx.core.graphics.toColorInt
import kotlin.math.cos
import kotlin.math.sin

class ColorDisplayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : View(context, attrs, defStyle) {

    enum class DisplayMode {
        SOLID_COLOR,
        LINEAR_GRADIENT
    }

    private var displayMode = DisplayMode.SOLID_COLOR
    private var solidColor = Color.parseColor("#000000")
    
    // Gradient properties
    private var startColor = Color.parseColor("#FF0000")
    private var endColor = Color.parseColor("#0000FF")
    private var gradientAngle = 0f // <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> bằng độ (0-360)

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    /**
     * Thiết lập chế độ hiển thị màu đơn
     */
    fun setSolidColor(color: Int) {
        this.displayMode = DisplayMode.SOLID_COLOR
        this.solidColor = color
        paint.shader = null
        invalidate()
    }

    /**
     * Thiết lập chế độ hiển thị gradient
     */
    fun setLinearGradient(startColor: Int, endColor: Int, angle: Float = 0f) {
        this.displayMode = DisplayMode.LINEAR_GRADIENT
        this.startColor = startColor
        this.endColor = endColor
        this.gradientAngle = angle % 360f
        updateGradientShader()
        invalidate()
    }

    /**
     * Cập nhật góc gradient (chỉ áp dụng khi đang ở chế độ gradient)
     */
    fun setGradientAngle(angle: Float) {
        if (displayMode == DisplayMode.LINEAR_GRADIENT) {
            this.gradientAngle = angle % 360f
            updateGradientShader()
            invalidate()
        }
    }

    /**
     * Lấy chế độ hiển thị hiện tại
     */
    fun getDisplayMode(): DisplayMode = displayMode

    private fun updateGradientShader() {
        if (width > 0 && height > 0) {
            val centerX = width / 2f
            val centerY = height / 2f
            val radius = maxOf(width, height) / 2f
            
            // Chuyển đổi góc từ độ sang radian
            val angleRad = Math.toRadians(gradientAngle.toDouble())
            
            // Tính toán điểm bắt đầu và kết thúc của gradient
            val startX = centerX - radius * cos(angleRad).toFloat()
            val startY = centerY - radius * sin(angleRad).toFloat()
            val endX = centerX + radius * cos(angleRad).toFloat()
            val endY = centerY + radius * sin(angleRad).toFloat()

            val gradient = LinearGradient(
                startX, startY, endX, endY,
                startColor, endColor,
                Shader.TileMode.CLAMP
            )
            
            paint.shader = gradient
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (displayMode == DisplayMode.LINEAR_GRADIENT) {
            updateGradientShader()
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        when (displayMode) {
            DisplayMode.SOLID_COLOR -> {
                paint.color = solidColor
                paint.shader = null
            }
            DisplayMode.LINEAR_GRADIENT -> {
                // Shader đã được thiết lập trong updateGradientShader()
            }
        }
        
        // Vẽ hình chữ nhật bo góc
        canvas.drawRoundRect(0f, 0f, width.toFloat(), height.toFloat(), 8f, 8f, paint)
    }
}