package pion.tech.pionbase.custom_view

import android.graphics.Color
import androidx.core.graphics.toColorInt
import pion.tech.pionbase.data.model.create_banner.ColorSelectedData
import pion.tech.pionbase.data.model.create_banner.ColorPickerType
import pion.tech.pionbase.data.model.create_banner.TextStyleData

/**
 * Extension functions for LEDView to provide additional utility methods
 */

/**
 * Apply text style data with animation
 */
fun LEDView.applyTextStyleDataWithAnimation(styleData: TextStyleData, animateChanges: Boolean = true) {
    if (animateChanges) {
        // Stop current animation before applying changes
        stopAnimation()
        applyTextStyleData(styleData)
        // Restart animation with new settings
        startAnimation()
    } else {
        applyTextStyleData(styleData)
    }
}


/**
 * Set outline color from hex string
 */
fun LEDView.setOutlineColorFromHex(hexColor: String) {
    try {
        setOutlineColor(hexColor.toColorInt())
    } catch (e: Exception) {
        // Fallback to red if color parsing fails
        setOutlineColor(Color.RED)
    }
}

/**
 * Set shadow color from hex string
 */
fun LEDView.setShadowColorFromHex(hexColor: String) {
    try {
        setShadowColor(hexColor.toColorInt())
    } catch (e: Exception) {
        // Fallback to gray if color parsing fails
        setShadowColor(Color.GRAY)
    }
}

/**
 * Set background color from hex string
 */
fun LEDView.setBackgroundColorFromHex(hexColor: String) {
    try {
        setBackgroundColor(hexColor.toColorInt())
    } catch (e: Exception) {
        // Fallback to black if color parsing fails
        setBackgroundColor(Color.BLACK)
    }
}

/**
 * Toggle text bold style
 */
fun LEDView.toggleBold() {
    val currentStyle = getCurrentTextStyleData()
    setTextBold(!currentStyle.isBold)
}

/**
 * Toggle text italic style
 */
fun LEDView.toggleItalic() {
    val currentStyle = getCurrentTextStyleData()
    setTextItalic(!currentStyle.isItalic)
}

/**
 * Toggle text underline style
 */
fun LEDView.toggleUnderline() {
    val currentStyle = getCurrentTextStyleData()
    setTextUnderline(!currentStyle.isUnderline)
}

/**
 * Clear all text formatting (bold, italic, underline)
 */
fun LEDView.clearTextFormatting() {
    setTextBold(false)
    setTextItalic(false)
    setTextUnderline(false)
}

/**
 * Reset to default style
 */
fun LEDView.resetToDefaultStyle() {
    applyTextStyleData(TextStyleData())
}

/**
 * Set text size in SP (scaled pixels)
 */
fun LEDView.setTextSizeSp(sizeSp: Float) {
    val density = context.resources.displayMetrics.scaledDensity
    setTextSize(sizeSp * density)
}

/**
 * Get text size in SP
 */
fun LEDView.getTextSizeSp(): Float {
    val density = context.resources.displayMetrics.scaledDensity
    val currentStyle = getCurrentTextStyleData()
    return currentStyle.textSize / density
}

/**
 * Enable/disable outline with default width
 */
fun LEDView.enableOutline(enable: Boolean, width: Float = 4f) {
    setOutlineWidth(if (enable) width else 0f)
}

/**
 * Enable/disable shadow with default radius
 */
fun LEDView.enableShadow(enable: Boolean, radius: Float = 4f) {
    setShadowRadius(if (enable) radius else 0f)
}

/**
 * Set animation speed in seconds
 */
fun LEDView.setAnimationSpeedSeconds(seconds: Float) {
    setAnimationSpeed((seconds * 1000).toLong())
}

/**
 * Check if any text styling is applied
 */
fun LEDView.hasTextStyling(): Boolean {
    val style = getCurrentTextStyleData()
    return style.isBold || style.isItalic || style.isUnderline || 
           style.outlineWidth > 0 || style.shadowRadius > 0
}

/**
 * Apply multiple style properties at once
 */
fun LEDView.applyMultipleStyles(
    bold: Boolean? = null,
    italic: Boolean? = null,
    underline: Boolean? = null,
    textColor: ColorSelectedData? = null,
    outlineWidth: Float? = null,
    outlineColor: String? = null,
    shadowRadius: Float? = null,
    shadowColor: String? = null
) {
    val currentStyle = getCurrentTextStyleData()
    
    val newStyle = currentStyle.copy(
        isBold = bold ?: currentStyle.isBold,
        isItalic = italic ?: currentStyle.isItalic,
        isUnderline = underline ?: currentStyle.isUnderline,
        textColor = textColor ?: currentStyle.textColor,
        outlineWidth = outlineWidth?.toInt() ?: currentStyle.outlineWidth,
        outlineColor = outlineColor ?: currentStyle.outlineColor,
        shadowRadius = shadowRadius?.toInt() ?: currentStyle.shadowRadius,
        shadowColor = shadowColor ?: currentStyle.shadowColor
    )
    
    applyTextStyleData(newStyle)
}