package pion.tech.pionbase.custom_view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import pion.tech.pionbase.R
import pion.tech.pionbase.data.model.create_banner.TextStyleData
import pion.tech.pionbase.data.model.create_banner.ColorSelectedData
import pion.tech.pionbase.data.model.create_banner.ColorPickerType
import kotlin.apply
import kotlin.let

class LEDView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val DEFAULT_TEXT_SIZE = 80f
        private const val DEFAULT_DOT_RADIUS = 8f
        private const val DEFAULT_DOT_SPACING = 2f
        private const val DEFAULT_ANIMATION_SPEED = 20000L
        private const val DEFAULT_SHADOW_OFFSET_X = 2f
        private const val DEFAULT_SHADOW_OFFSET_Y = 2f
    }

    // Paint objects for different layers
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val outlinePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val xferPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    
    // Bitmaps and canvas for layers
    private var backgroundBitmap: Bitmap? = null
    private var textBitmap: Bitmap? = null
    private var xferBitmap: Bitmap? = null
    private var resultBitmap: Bitmap? = null
    private var compositeBitmap: Bitmap? = null
    private var backgroundCanvas: Canvas? = null
    private var textCanvas: Canvas? = null
    private var resultCanvas: Canvas? = null
    private var compositeCanvas: Canvas? = null
    
    // Text properties from TextStyleData
    private var displayText = "LED Banner View 🚀✨"
    private var textSize = DEFAULT_TEXT_SIZE
    private var textColorData = ColorSelectedData(
        colorPickerType = ColorPickerType.SOLID,
        solidColor = "#FFFFFFFF".toColorInt()
    )
    private var isBold = false
    private var isItalic = false
    private var isUnderline = false
    private var fontFamily = "Roboto"
    private var outlineWidth = 0f
    private var outlineColor = Color.RED
    private var shadowRadius = 0f
    private var shadowColor = Color.GRAY
    private var backgroundColor = Color.BLACK
    
    // Animation properties
    private var textX = 0f
    private var textWidth = 0f
    private var animator: ValueAnimator? = null
    private var animationSpeed = DEFAULT_ANIMATION_SPEED
    
    // Xfermode properties
    private var dotsBitmap: Bitmap? = null
    private val xferMode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)

    // Dots pattern properties
    private var dotRadius = DEFAULT_DOT_RADIUS
    private var dotSpacing = DEFAULT_DOT_SPACING
    
    init {
        // Parse attributes if provided
        attrs?.let { parseAttributes(it) }
        
        setupPaints()

        // Initialize text position
        textX = 0f
    }
    
    private fun parseAttributes(attrs: AttributeSet) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.LEDView)
        
        try {
            // Parse text properties
            displayText = typedArray.getString(R.styleable.LEDView_led_text) ?: displayText
            textSize = typedArray.getDimension(R.styleable.LEDView_led_textSize, textSize)
            // For XML attributes, we still support simple color for backward compatibility
            val xmlTextColor = typedArray.getColor(R.styleable.LEDView_led_textColor, Color.WHITE)
            textColorData = ColorSelectedData(
                colorPickerType = ColorPickerType.SOLID,
                solidColor = xmlTextColor
            )
            isBold = typedArray.getBoolean(R.styleable.LEDView_led_isBold, isBold)
            isItalic = typedArray.getBoolean(R.styleable.LEDView_led_isItalic, isItalic)
            isUnderline = typedArray.getBoolean(R.styleable.LEDView_led_isUnderline, isUnderline)
            fontFamily = typedArray.getString(R.styleable.LEDView_led_fontFamily) ?: fontFamily
            
            // Parse outline properties
            outlineWidth = typedArray.getDimension(R.styleable.LEDView_led_outlineWidth, outlineWidth)
            outlineColor = typedArray.getColor(R.styleable.LEDView_led_outlineColor, outlineColor)
            
            // Parse shadow properties
            shadowRadius = typedArray.getDimension(R.styleable.LEDView_led_shadowRadius, shadowRadius)
            shadowColor = typedArray.getColor(R.styleable.LEDView_led_shadowColor, shadowColor)
            
            // Parse other properties
            backgroundColor = typedArray.getColor(R.styleable.LEDView_led_backgroundColor, backgroundColor)
            animationSpeed = typedArray.getInteger(R.styleable.LEDView_led_animationSpeed, animationSpeed.toInt()).toLong()
            dotRadius = typedArray.getDimension(R.styleable.LEDView_led_dotRadius, dotRadius)
            dotSpacing = typedArray.getDimension(R.styleable.LEDView_led_dotSpacing, dotSpacing)
            
        } finally {
            typedArray.recycle()
        }
    }
    
    private fun setupPaints() {
        // Setup text paint with all style properties
        updateTextPaint()
        
        // Setup outline paint
        outlinePaint.apply {
            color = outlineColor
            style = Paint.Style.STROKE
            strokeWidth = outlineWidth
            isAntiAlias = true
        }
        
        // Setup shadow paint
        shadowPaint.apply {
            color = shadowColor
            isAntiAlias = true
        }
        
        // Setup background paint
        backgroundPaint.apply {
            color = backgroundColor
            style = Paint.Style.FILL
        }
        
        // Setup xfer paint
        xferPaint.apply {
            isAntiAlias = true
        }
    }
    
    private fun updateTextPaint() {
        textPaint.apply {
            // Apply color or gradient based on textColorData
            applyTextColor(this)
            textSize = <EMAIL>
            isAntiAlias = true
            
            // Apply text style
            var style = Typeface.NORMAL
            if (isBold && isItalic) {
                style = Typeface.BOLD_ITALIC
            } else if (isBold) {
                style = Typeface.BOLD
            } else if (isItalic) {
                style = Typeface.ITALIC
            }
            
            // Apply font family
            typeface = when (fontFamily.lowercase()) {
                "serif" -> Typeface.create(Typeface.SERIF, style)
                "sans-serif" -> Typeface.create(Typeface.SANS_SERIF, style)
                "monospace" -> Typeface.create(Typeface.MONOSPACE, style)
                else -> Typeface.create(Typeface.DEFAULT, style)
            }
            
            // Apply underline
            isUnderlineText = isUnderline
            
            // Apply shadow if needed
            if (shadowRadius > 0) {
                setShadowLayer(shadowRadius, DEFAULT_SHADOW_OFFSET_X, DEFAULT_SHADOW_OFFSET_Y, shadowColor)
            } else {
                clearShadowLayer()
            }
        }
        
        // Update outline paint
        outlinePaint.apply {
            color = outlineColor
            strokeWidth = outlineWidth
            textSize = <EMAIL>
            typeface = textPaint.typeface
        }
    }
    
    private fun applyTextColor(paint: Paint) {
        when (textColorData.colorPickerType) {
            ColorPickerType.SOLID -> {

                try {
                    paint.color = textColorData.solidColor ?: "#FFFFFFFF".toColorInt()
                    paint.shader = null // Clear any existing shader
                } catch (e: Exception) {
                    paint.color = Color.WHITE // Fallback
                    paint.shader = null
                }
            }
            ColorPickerType.GRADIENT -> {
                // Apply gradient color
                createTextGradient(paint)
            }
        }
    }
    
    private fun createTextGradient(paint: Paint) {
        val startColorHex = textColorData.startGradientColor ?: "#FFFFFFFF".toColorInt()
        val endColorHex = textColorData.endGradientColor ?: "#FF000000".toColorInt()
        val angle = textColorData.angleGradient ?: 0f
        
        try {
            val startColor = startColorHex
            val endColor = endColorHex
            
            // Use view dimensions for gradient calculation since text moves
            val viewWidth = width.toFloat()
            val viewHeight = height.toFloat()
            
            if (viewWidth <= 0 || viewHeight <= 0) {
                // Fallback to simple horizontal gradient if view dimensions not available
                val gradient = LinearGradient(
                    0f, 0f, 200f, 0f,
                    startColor, endColor,
                    Shader.TileMode.CLAMP
                )
                paint.shader = gradient
                paint.color = Color.WHITE
                return
            }
            
            // Calculate gradient coordinates based on angle
            val angleRad = Math.toRadians(angle.toDouble())
            
            // Calculate start and end points for the gradient across the view
            val centerX = viewWidth / 2f
            val centerY = viewHeight / 2f
            val radius = kotlin.math.sqrt((viewWidth * viewWidth + viewHeight * viewHeight).toDouble()).toFloat() / 2f
            
            val startX = centerX - radius * kotlin.math.cos(angleRad).toFloat()
            val startY = centerY - radius * kotlin.math.sin(angleRad).toFloat()
            val endX = centerX + radius * kotlin.math.cos(angleRad).toFloat()
            val endY = centerY + radius * kotlin.math.sin(angleRad).toFloat()
            
            val gradient = LinearGradient(
                startX, startY, endX, endY,
                startColor, endColor,
                Shader.TileMode.CLAMP
            )
            
            paint.shader = gradient
            paint.color = Color.WHITE // Set a base color (will be overridden by shader)
        } catch (e: Exception) {
            // Fallback to solid white color if gradient creation fails
            paint.color = Color.WHITE
            paint.shader = null
        }
    }

    fun createDotPatternBitmap(
        width: Int,
        height: Int,
        dotRadius: Float = 10f,
        spacing: Float = 20f,
        dotColor: Int = Color.WHITE, // Chấm màu trắng (alpha = 255) để giữ lại nội dung
        bgColor: Int = Color.TRANSPARENT // Background trong suốt để ẩn nội dung
    ): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)

        // Vẽ background trong suốt (sẽ ẩn nội dung)
        canvas.drawColor(bgColor)

        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.color = dotColor // Màu trắng để giữ lại nội dung
        paint.style = Paint.Style.FILL

        // Tính toán khoảng cách giữa các chấm
        val stepX = dotRadius * 2 + spacing
        val stepY = dotRadius * 2 + spacing

        // Tính toán offset để căn giữa pattern
        val numDotsX = (width / stepX).toInt()
        val numDotsY = (height / stepY).toInt()
        val offsetX = (width - (numDotsX - 1) * stepX) / 2f
        val offsetY = (height - (numDotsY - 1) * stepY) / 2f

        // Vẽ các chấm tròn trắng (alpha = 255) - đây sẽ là các "lỗ" LED
        for (row in 0 until numDotsY) {
            for (col in 0 until numDotsX) {
                val x = offsetX + col * stepX
                val y = offsetY + row * stepY
                canvas.drawCircle(x, y, dotRadius, paint)
            }
        }

        return bitmap
    }

    private fun startTextAnimation() {
        animator?.cancel()

        if (width > 0 && textWidth > 0) {
            animator = ValueAnimator.ofFloat(0f, 1f).apply {
                duration = animationSpeed
                repeatCount = ValueAnimator.INFINITE
                repeatMode = ValueAnimator.RESTART
                addUpdateListener { animation ->
                    val progress = animation.animatedValue as Float
                    // Start from right edge, move to left edge
                    textX = width.toFloat() - (textWidth + width) * progress
                    invalidate()
                }
            }
            animator?.start()
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        if (w > 0 && h > 0) {
            // Giải phóng bitmaps cũ nếu có
            backgroundBitmap?.recycle()
            textBitmap?.recycle()
            xferBitmap?.recycle()
            resultBitmap?.recycle()
            compositeBitmap?.recycle()
            dotsBitmap?.recycle()

            // Create bitmaps for each layer
            backgroundBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            textBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            xferBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            resultBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            compositeBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)

            backgroundCanvas = Canvas(backgroundBitmap!!)
            textCanvas = Canvas(textBitmap!!)
            resultCanvas = Canvas(resultBitmap!!)
            compositeCanvas = Canvas(compositeBitmap!!)

            // Tạo dots bitmap động với kích thước view
            dotsBitmap = createDotPatternBitmap(
                width = w,
                height = h,
                dotRadius = dotRadius,
                spacing = dotSpacing,
                dotColor = Color.WHITE, // Chấm trắng để giữ lại nội dung
                bgColor = Color.TRANSPARENT // Background trong suốt để ẩn nội dung
            )

            // Calculate text width
            textWidth = textPaint.measureText(displayText)

            // Start animation
            startTextAnimation()
        }
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val w = width
        val h = height

        if (w <= 0 || h <= 0 || backgroundBitmap == null || textBitmap == null || resultBitmap == null) return

        // Clear all layers
        backgroundCanvas?.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        textCanvas?.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        resultCanvas?.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        compositeCanvas?.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)

        // Layer 1: Draw background
        drawBackgroundLayer(backgroundCanvas!!, w, h)

        // Layer 2: Draw text
        drawTextLayer(textCanvas!!, w, h)

        // Layer 3: Apply xfermode effect to result bitmap
        applyMaskEffect(w, h)

        canvas.drawBitmap(resultBitmap!!, 0f, 0f, null)
    }
    
    private fun drawBackgroundLayer(canvas: Canvas, width: Int, height: Int) {
        // Create gradient background
        val gradient = LinearGradient(
            0f, 0f, width.toFloat(), height.toFloat(),
            intArrayOf(
                Color.parseColor("#000000"),
                Color.parseColor("#000FFF"),
                Color.parseColor("#FFFFFF"),
            ),
            null,
            Shader.TileMode.CLAMP
        )
        backgroundPaint.shader = gradient
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), backgroundPaint)
    }
    
    private fun drawTextLayer(canvas: Canvas, width: Int, height: Int) {
        // Calculate text position
        val textY = height / 2f + textSize / 3f
        
        // Draw outline if enabled
        if (outlineWidth > 0) {
            canvas.drawText(displayText, textX, textY, outlinePaint)
        }
        
        // Draw main text
        canvas.drawText(displayText, textX, textY, textPaint)
    }
    
    private fun applyMaskEffect(width: Int, height: Int) {
        // Bước 1: Composite background + text vào compositeBitmap
        compositeCanvas?.let { canvas ->
            // Vẽ background gradient
            canvas.drawBitmap(backgroundBitmap!!, 0f, 0f, null)

            // Vẽ text
            canvas.drawBitmap(textBitmap!!, 0f, 0f, null)

            // Áp dụng mask với dots pattern
            dotsBitmap?.let { dots ->
                val maskPaint = Paint(Paint.ANTI_ALIAS_FLAG)
                maskPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)

                // Áp dụng mask - chỉ những vùng có chấm trắng sẽ giữ lại nội dung
                canvas.drawBitmap(dots, 0f, 0f, maskPaint)
            }
        }

        // Bước 2: Vẽ kết quả cuối cùng lên resultBitmap
        resultCanvas?.let { canvas ->
            // Background đen cho LED panel
            canvas.drawColor(Color.BLACK)

            // Vẽ nội dung đã được mask
            compositeBitmap?.let { composite ->
                canvas.drawBitmap(composite, 0f, 0f, null)
            }
        }
    }
    
    // Public methods to control the LED view
    fun setText(text: String) {
        displayText = text
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setTextColor(color: Int) {
        textColorData = ColorSelectedData(
            colorPickerType = ColorPickerType.SOLID,
            solidColor = color
        )
        updateTextPaint()
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setTextColorData(colorData: ColorSelectedData) {
        textColorData = colorData
        updateTextPaint()
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setTextGradient(startColor: Int, endColor: Int, angle: Float = 0f) {
        textColorData = ColorSelectedData(
            colorPickerType = ColorPickerType.GRADIENT,
            startGradientColor = startColor,
            endGradientColor = endColor,
            angleGradient = angle
        )
        updateTextPaint()
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }

    fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            resources.displayMetrics
        )
    }

    fun setTextSize(size: Float) {
        textSize = dpToPx(size)
        updateTextPaint()
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setTextBold(bold: Boolean) {
        isBold = bold
        updateTextPaint()
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setTextItalic(italic: Boolean) {
        isItalic = italic
        updateTextPaint()
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setTextUnderline(underline: Boolean) {
        isUnderline = underline
        updateTextPaint()
        invalidate()
    }
    
    fun setFontFamily(family: String) {
        fontFamily = family
        updateTextPaint()
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setOutlineWidth(width: Float) {
        outlineWidth = width
        updateTextPaint()
        invalidate()
    }
    
    fun setOutlineColor(color: Int) {
        outlineColor = color
        updateTextPaint()
        invalidate()
    }
    
    fun setShadowRadius(radius: Float) {
        shadowRadius = radius
        updateTextPaint()
        invalidate()
    }
    
    fun setShadowColor(color: Int) {
        shadowColor = color
        updateTextPaint()
        invalidate()
    }
    
    override fun setBackgroundColor(color: Int) {
        backgroundColor = color
        backgroundPaint.color = color
        invalidate()
    }
    
    // Method to apply TextStyleData
    fun applyTextStyleData(styleData: TextStyleData) {
        textSize = dpToPx(styleData.textSize.toFloat())
        textColorData = styleData.textColor
        isBold = styleData.isBold
        isItalic = styleData.isItalic
        isUnderline = styleData.isUnderline
        fontFamily = styleData.fontFamily
        outlineWidth = styleData.outlineWidth.toFloat()
        outlineColor = styleData.outlineColor.toColorInt()
        shadowRadius = styleData.shadowRadius.toFloat()
        shadowColor = styleData.shadowColor.toColorInt()
        
        updateTextPaint()
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }

    fun pxToDp(px: Float): Float {
        return px / resources.displayMetrics.density
    }

    // Method to get current style as TextStyleData
    fun getCurrentTextStyleData(): TextStyleData {
        return TextStyleData(
            textSize = pxToDp(textSize).toInt(),
            textColor = textColorData,
            isBold = isBold,
            isItalic = isItalic,
            isUnderline = isUnderline,
            fontFamily = fontFamily,
            outlineWidth = outlineWidth.toInt(),
            outlineColor = String.format("#%08X", outlineColor),
            shadowRadius = shadowRadius.toInt(),
            shadowColor = String.format("#%08X", shadowColor)
        )
    }
    
    fun setAnimationSpeed(speedMs: Long) {
        animationSpeed = speedMs
        if (animator?.isRunning == true) {
            startTextAnimation()
        }
    }
    
    fun startAnimation() {
        startTextAnimation()
    }
    
    fun stopAnimation() {
        animator?.cancel()
    }

    // Phương thức để tùy chỉnh dots pattern
    fun setDotRadius(radius: Float) {
        dotRadius = radius
        recreateDotsPattern()
    }

    fun setDotSpacing(spacing: Float) {
        dotSpacing = spacing
        recreateDotsPattern()
    }

    fun setDotPattern(radius: Float, spacing: Float) {
        dotRadius = radius
        dotSpacing = spacing
        recreateDotsPattern()
    }

    private fun recreateDotsPattern() {
        // Tạo lại dots bitmap nếu view đã được khởi tạo
        if (width > 0 && height > 0) {
            dotsBitmap?.recycle()
            dotsBitmap = createDotPatternBitmap(
                width = width,
                height = height,
                dotRadius = dotRadius,
                spacing = dotSpacing,
                dotColor = Color.WHITE, // Chấm trắng để giữ lại nội dung
                bgColor = Color.TRANSPARENT // Background trong suốt để ẩn nội dung
            )
            invalidate()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animator?.cancel()

        // Giải phóng tất cả bitmaps để tránh memory leak
        backgroundBitmap?.recycle()
        textBitmap?.recycle()
        xferBitmap?.recycle()
        resultBitmap?.recycle()
        compositeBitmap?.recycle()
        dotsBitmap?.recycle()

        backgroundBitmap = null
        textBitmap = null
        xferBitmap = null
        resultBitmap = null
        compositeBitmap = null
        dotsBitmap = null
        backgroundCanvas = null
        textCanvas = null
        resultCanvas = null
        compositeCanvas = null
    }
}
