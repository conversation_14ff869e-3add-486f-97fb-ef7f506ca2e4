package pion.tech.pionbase.data.model.font

data class FontUIModel(
    val name: String,
    val fontLight: String? = null,
    val fontRegular: String? = null,
    val fontMedium: String? = null,
    val fontSemiBold: String? = null,
    val fontBold: String? = null,
)

fun FontDtoModel.toPresentation() =
    FontUIModel(
        name = this.name,
        fontLight = this.fontLight,
        fontRegular = this.fontRegular,
        fontMedium = this.fontMedium,
        fontSemiBold = this.fontSemiBold,
        fontBold = this.fontBold,
    )
