package pion.tech.pionbase.data.model.create_banner

import androidx.core.graphics.toColorInt

data class TextStyleData(
    val textSize: Int = 72,
    val textColor: ColorSelectedData = ColorSelectedData(
        colorPickerType = ColorPickerType.SOLID,
        solidColor = "#FFFFFFFF".toColorInt()
    ),
    val isBold: Boolean = false,
    val isItalic: Boolean = false,
    val isUnderline: Boolean = false,
    val fontFamily: String = "Roboto",
    val outlineWidth: Int = 0,
    val outlineColor: String = "#FFFF0000",
    val shadowRadius: Int = 0,
    val shadowColor: String = "#FF666666"
)