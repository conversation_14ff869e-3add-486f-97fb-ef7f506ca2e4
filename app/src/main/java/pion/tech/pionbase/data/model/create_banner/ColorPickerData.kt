package pion.tech.pionbase.data.model.create_banner

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class SolidColorData(
    val color: Int,
    val isCustomColorPicker: Boolean = false,
    val isSelected: Boolean = false
)

data class GradientColorData(
    val startColor: Int,
    val endColor: Int,
    val angle: Float = 0f,
    val isSelected: Boolean = false
)

@Parcelize
data class ColorSelectedData(
    val colorPickerType: ColorPickerType = ColorPickerType.SOLID,
    val solidColor: Int? = null,
    val startGradientColor: Int? = null,
    val endGradientColor: Int? = null,
    val angleGradient: Float? = null
) : Parcelable

enum class ColorPickerType(val value: String) {
    SOLID("solid"),
    GRADIENT("gradient"),
}