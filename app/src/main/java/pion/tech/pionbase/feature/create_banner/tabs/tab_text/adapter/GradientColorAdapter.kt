package pion.tech.pionbase.feature.create_banner.tabs.tab_text.adapter

import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import pion.tech.pionbase.R
import pion.tech.pionbase.base.BaseListAdapter
import pion.tech.pionbase.base.createDiffCallback
import pion.tech.pionbase.data.model.create_banner.GradientColorData
import pion.tech.pionbase.databinding.ItemGradientPickerBinding
import pion.tech.pionbase.util.setPreventDoubleClick

class GradientColorAdapter(
    private val onGradientSelected: (GradientColorData) -> Unit
) : BaseListAdapter<GradientColorData, ItemGradientPickerBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.startColor == newItem.startColor && oldItem.endColor == newItem.endColor && oldItem.angle == newItem.angle },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
    )
) {

    override fun getLayoutRes(viewType: Int): Int {
        return R.layout.item_gradient_picker
    }

    override fun bindView(
        binding: ItemGradientPickerBinding,
        item: GradientColorData,
        position: Int
    ) = with(binding) {
        // Create gradient drawable
        val gradientDrawable = GradientDrawable().apply {
            orientation = when (item.angle) {
                0f -> GradientDrawable.Orientation.LEFT_RIGHT
                45f -> GradientDrawable.Orientation.BL_TR
                90f -> GradientDrawable.Orientation.BOTTOM_TOP
                135f -> GradientDrawable.Orientation.BR_TL
                180f -> GradientDrawable.Orientation.RIGHT_LEFT
                225f -> GradientDrawable.Orientation.TR_BL
                270f -> GradientDrawable.Orientation.TOP_BOTTOM
                315f -> GradientDrawable.Orientation.TL_BR
                else -> GradientDrawable.Orientation.LEFT_RIGHT
            }
            colors = intArrayOf(item.startColor, item.endColor)
            cornerRadius = 8f * binding.root.context.resources.displayMetrics.density
        }

        // Set gradient background
        viewGradientBackground.background = gradientDrawable

        // Show/hide selection border
        viewSelectionBorder.isVisible = item.isSelected

        // Set click listener
        root.setPreventDoubleClick {
            onGradientSelected(item)
        }
    }
}