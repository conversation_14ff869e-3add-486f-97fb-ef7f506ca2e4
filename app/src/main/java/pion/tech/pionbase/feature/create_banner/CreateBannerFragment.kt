package pion.tech.pionbase.feature.create_banner

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import pion.tech.pionbase.base.BaseFragment
import pion.tech.pionbase.custom_view.applyTextStyleDataWithAnimation
import pion.tech.pionbase.data.model.create_banner.CreateBannerScreenTab
import pion.tech.pionbase.databinding.FragmentCreateBannerBinding
import pion.tech.pionbase.util.collectFlowOnView
import pion.tech.pionbase.util.handleUiState
import pion.tech.pionbase.util.setPreventDoubleClick

@AndroidEntryPoint
class CreateBannerFragment : BaseFragment<FragmentCreateBannerBinding, CreateBannerViewModel>(
    FragmentCreateBannerBinding::inflate,
    CreateBannerViewModel::class.java,
) {
    override fun init(view: View) {
        setUpUISelectedTab(CreateBannerScreenTab.TEXT)
        initView()
        displayTextChangedEvent()
        setUpTabClickEvents()
    }

    private fun setUpTabClickEvents() {
        binding.tvTabText.setPreventDoubleClick {
            setUpUISelectedTab(CreateBannerScreenTab.TEXT)
        }

        binding.tvBackground.setPreventDoubleClick {
            setUpUISelectedTab(CreateBannerScreenTab.BACKGROUND)
        }

        binding.tvTabEffect.setPreventDoubleClick {
            setUpUISelectedTab(CreateBannerScreenTab.EFFECT)
        }

        binding.tvTabMusic.setPreventDoubleClick {
            setUpUISelectedTab(CreateBannerScreenTab.MUSIC)
        }
    }

    override fun subscribeObserver(view: View) {
        commonViewModel.getCategoryUiState.collectFlowOnView(viewLifecycleOwner) {
            it.handleUiState(
                onLoading = { showHideLoading(true) },
                onSuccess = { listAppCategory ->
                    showHideLoading(false)
                    val fontsCategoryId =
                        listAppCategory.firstOrNull { item -> item.name == "Fonts" }?.id
                    if (fontsCategoryId != null) {
                        commonViewModel.getFont(fontsCategoryId)
                    }
                },
                onError = { showHideLoading(false) },
            )
        }

        createBannerCommonViewModel.textStyleData.collectFlowOnView(viewLifecycleOwner) { textStyleData ->
            binding.ledView.applyTextStyleDataWithAnimation(textStyleData, false)
        }
    }

    companion object {
        const val TAG = "CreateBannerFragment"
        const val DEFAULT_DISPLAY_TEXT = "LED Banner View 🚀✨"
    }
}