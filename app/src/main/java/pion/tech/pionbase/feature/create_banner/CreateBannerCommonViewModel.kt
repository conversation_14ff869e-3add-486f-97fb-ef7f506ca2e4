package pion.tech.pionbase.feature.create_banner

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import pion.tech.pionbase.base.BaseViewModel
import pion.tech.pionbase.data.model.create_banner.ColorSelectedData
import pion.tech.pionbase.data.model.create_banner.CreateBannerScreenTab
import pion.tech.pionbase.data.model.create_banner.FontItem
import pion.tech.pionbase.data.model.create_banner.TextStyleData
import javax.inject.Inject

@HiltViewModel
class CreateBannerCommonViewModel @Inject constructor() : BaseViewModel() {

    companion object {
        const val MIN_TEXT_SIZE = 14
        const val MAX_TEXT_SIZE = 300
        const val DEFAULT_TEXT_SIZE = 50
        const val MIN_OUTLINE_WIDTH = 0
        const val MAX_OUTLINE_WIDTH = 10
        const val MIN_SHADOW_RADIUS = 0
        const val MAX_SHADOW_RADIUS = 20
    }

    private val _textStyleData = MutableStateFlow(TextStyleData())
    val textStyleData: StateFlow<TextStyleData> = _textStyleData.asStateFlow()

    private val _availableFonts = MutableStateFlow<List<FontItem>>(emptyList())
    val availableFonts: StateFlow<List<FontItem>> = _availableFonts.asStateFlow()

    private val _selectedFontIndex = MutableStateFlow(0)
    val selectedFontIndex: StateFlow<Int> = _selectedFontIndex.asStateFlow()

    init {
        initializeFonts()
    }

    private fun initializeFonts() {
        val fonts = listOf(
            FontItem("roboto", "Roboto"),
            FontItem("arial", "Arial"),
            FontItem("times_new_roman", "Times New Roman"),
            FontItem("helvetica", "Helvetica"),
            FontItem("georgia", "Georgia"),
            FontItem("verdana", "Verdana"),
            FontItem("courier_new", "Courier New"),
            FontItem("comic_sans", "Comic Sans MS")
        )
        _availableFonts.update { fonts }
    }

    fun updateTextSize(size: Int) {
        _textStyleData.update { currentData ->
            currentData.copy(textSize = size.coerceIn(MIN_TEXT_SIZE, MAX_TEXT_SIZE))
        }
    }

    fun updateTextColor(color: ColorSelectedData) {
        _textStyleData.update { currentData ->
            currentData.copy(textColor = color)
        }
    }

    fun toggleTextBold() {
        _textStyleData.update { currentData ->
            currentData.copy(isBold = !currentData.isBold)
        }
    }

    fun toggleTextItalic() {
        _textStyleData.update { currentData ->
            currentData.copy(isItalic = !currentData.isItalic)
        }
    }

    fun toggleTextUnderline() {
        _textStyleData.update { currentData ->
            currentData.copy(isUnderline = !currentData.isUnderline)
        }
    }

    fun clearTextFormat() {
        _textStyleData.update { currentData ->
            currentData.copy(
                isBold = false,
                isItalic = false,
                isUnderline = false
            )
        }
    }

    fun updateFontFamily(fontIndex: Int) {
        val fonts = _availableFonts.value
        if (fontIndex in fonts.indices) {
            _selectedFontIndex.update { fontIndex }
            _textStyleData.update { currentData ->
                currentData.copy(fontFamily = fonts[fontIndex].name)
            }
        }
    }

    fun updateOutlineWidth(width: Int) {
        _textStyleData.update { currentData ->
            currentData.copy(outlineWidth = width.coerceIn(MIN_OUTLINE_WIDTH, MAX_OUTLINE_WIDTH))
        }
    }

    fun updateOutlineColor(color: String) {
        _textStyleData.update { currentData ->
            currentData.copy(outlineColor = color)
        }
    }

    fun updateShadowRadius(radius: Int) {
        _textStyleData.update { currentData ->
            currentData.copy(shadowRadius = radius.coerceIn(MIN_SHADOW_RADIUS, MAX_SHADOW_RADIUS))
        }
    }

    fun updateShadowColor(color: String) {
        _textStyleData.update { currentData ->
            currentData.copy(shadowColor = color)
        }
    }

}
