package pion.tech.pionbase.feature.create_banner.tabs.tab_text.adapter

import android.graphics.drawable.GradientDrawable
import androidx.core.view.isVisible
import pion.tech.pionbase.R
import pion.tech.pionbase.base.BaseListAdapter
import pion.tech.pionbase.base.createDiffCallback
import pion.tech.pionbase.data.model.create_banner.SolidColorData
import pion.tech.pionbase.databinding.ItemColorPickerBinding
import pion.tech.pionbase.util.setPreventDoubleClick

class SolidColorAdapter(
    private val onColorSelected: (SolidColorData) -> Unit
) : BaseListAdapter<SolidColorData, ItemColorPickerBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.color == newItem.color && oldItem.isCustomColorPicker == newItem.isCustomColorPicker },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
    )
) {

    override fun getLayoutRes(viewType: Int): Int {
        return R.layout.item_color_picker
    }

    override fun bindView(
        binding: ItemColorPickerBinding,
        item: SolidColorData,
        position: Int
    ) = with(binding) {
        // Set background color
        val solidDrawable = GradientDrawable().apply {
            setColor(item.color)
            cornerRadius = 8f * binding.root.context.resources.displayMetrics.density
        }

        viewColorBackground.background = solidDrawable

        // Show/hide selection border
        viewSelectionBorder.isVisible = item.isSelected

        // Show/hide color wheel icon for custom color picker
        ivColorWheel.isVisible = item.isCustomColorPicker

        // Set click listener
        root.setPreventDoubleClick {
            onColorSelected(item)
        }
    }
}