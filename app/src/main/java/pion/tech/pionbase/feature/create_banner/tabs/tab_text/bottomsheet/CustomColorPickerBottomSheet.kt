package pion.tech.pionbase.feature.create_banner.tabs.tab_text.bottomsheet

import android.graphics.Color
import android.os.Bundle
import com.skydoves.colorpickerview.ColorEnvelope
import com.skydoves.colorpickerview.listeners.ColorEnvelopeListener
import dagger.hilt.android.AndroidEntryPoint
import pion.tech.pionbase.R
import pion.tech.pionbase.base.BaseBottomSheetDialogFragment
import pion.tech.pionbase.data.model.create_banner.ColorPickerType
import pion.tech.pionbase.data.model.create_banner.ColorSelectedData
import pion.tech.pionbase.databinding.BottomSheetCustomColorPickerBinding
import pion.tech.pionbase.util.setPreventDoubleClick

@AndroidEntryPoint
class CustomColorPickerBottomSheet : BaseBottomSheetDialogFragment<BottomSheetCustomColorPickerBinding>(
    R.layout.bottom_sheet_custom_color_picker
) {
    
    companion object {
        private const val ARG_CURRENT_COLOR = "current_color"
        private const val ARG_TITLE = "title"

        fun newInstance(
            currentColor: Int = Color.RED,
            title: String = ""
        ): CustomColorPickerBottomSheet {
            return CustomColorPickerBottomSheet().apply {
                arguments = Bundle().apply {
                    putInt(ARG_CURRENT_COLOR, currentColor)
                    putString(ARG_TITLE, title)
                }
            }
        }
    }
    
    private var onColorSelected: ((ColorSelectedData) -> Unit)? = null
    private var currentColor: Int = Color.RED
    private var title: String = "Text Color"
    private var selectedColor: Int = Color.RED
    
    fun setOnColorSelectedListener(listener: (ColorSelectedData) -> Unit) {
        onColorSelected = listener
    }
    
    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        
        arguments?.let { args ->
            currentColor = args.getInt(ARG_CURRENT_COLOR, Color.RED)
            title = args.getString(ARG_TITLE, getString(R.string.text_color))
        }
        
        selectedColor = currentColor
    }
    
    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        
        binding.tvTitle.text = title
        
        // Setup color picker
        binding.colorPickerView.setColorListener(object : ColorEnvelopeListener {
            override fun onColorSelected(envelope: ColorEnvelope?, fromUser: Boolean) {
                envelope?.let {
                    selectedColor = it.color
                }
            }
        })
        
        // Set initial color
        binding.colorPickerView.setInitialColor(currentColor)
    }
    
    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        
        binding.btnClose.setPreventDoubleClick {
            dismiss()
        }
        
        binding.btnDone.setPreventDoubleClick {
            onColorSelected?.invoke(
                ColorSelectedData(
                    colorPickerType = ColorPickerType.SOLID,
                    solidColor = selectedColor
                )
            )
            dismiss()
        }
    }
}