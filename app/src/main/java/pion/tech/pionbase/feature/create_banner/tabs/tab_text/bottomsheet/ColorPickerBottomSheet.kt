package pion.tech.pionbase.feature.create_banner.tabs.tab_text.bottomsheet

import android.graphics.Color
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import dagger.hilt.android.AndroidEntryPoint
import pion.tech.pionbase.R
import pion.tech.pionbase.base.BaseBottomSheetDialogFragment
import pion.tech.pionbase.data.model.create_banner.ColorPickerType
import pion.tech.pionbase.data.model.create_banner.ColorSelectedData
import pion.tech.pionbase.data.model.create_banner.GradientColorData
import pion.tech.pionbase.data.model.create_banner.SolidColorData
import pion.tech.pionbase.databinding.BottomSheetColorPickerBinding
import pion.tech.pionbase.feature.create_banner.tabs.tab_text.adapter.GradientColorAdapter
import pion.tech.pionbase.feature.create_banner.tabs.tab_text.adapter.SolidColorAdapter
import pion.tech.pionbase.util.parcelable
import pion.tech.pionbase.util.setPreventDoubleClick

@AndroidEntryPoint
class ColorPickerBottomSheet : BaseBottomSheetDialogFragment<BottomSheetColorPickerBinding>(
    R.layout.bottom_sheet_color_picker
) {
    
    companion object {
        private const val ARG_CURRENT_COLOR = "current_color"
        private const val ARG_TITLE = "title"
        private const val ARG_SUPPORT_GRADIENT = "support_gradient"

        fun newInstance(
            currentColor: ColorSelectedData,
            title: String = "",
            supportGradient: Boolean = false,
        ): ColorPickerBottomSheet {
            return ColorPickerBottomSheet().apply {
                arguments = Bundle().apply {
                    putParcelable(ARG_CURRENT_COLOR, currentColor)
                    putString(ARG_TITLE, title)
                    putBoolean(ARG_SUPPORT_GRADIENT, supportGradient)
                }
            }
        }
    }
    private var onColorSelected: ((ColorSelectedData) -> Unit)? = null
    private var currentColor: ColorSelectedData = ColorSelectedData()
    private var title: String = "Text Color"

    private var supportGradient: Boolean = false

    private lateinit var solidColorAdapter: SolidColorAdapter
    private lateinit var gradientColorAdapter: GradientColorAdapter
    
    private val solidColors = mutableListOf<SolidColorData>()
    private val gradientColors = mutableListOf<GradientColorData>()
    
    fun setOnColorSelectedListener(listener: (ColorSelectedData) -> Unit) {
        onColorSelected = listener
    }
    
    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        
        arguments?.let { args ->
            currentColor = args.parcelable(ARG_CURRENT_COLOR) ?: ColorSelectedData()
            title = args.getString(ARG_TITLE, getString(R.string.text_color))
            supportGradient = args.getBoolean(ARG_SUPPORT_GRADIENT, false)
        }
        
        setupSolidColors()
        setupGradientColors()
    }
    
    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        
        binding.tvTitle.text = title
        setupRecyclerViews()
    }
    
    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        
        binding.btnClose.setPreventDoubleClick {
            dismiss()
        }
    }
    
    private fun setupSolidColors() {
        val colors = arrayOf(
            Color.RED, Color.GREEN, Color.BLUE, Color.YELLOW,
            Color.CYAN, Color.MAGENTA, Color.BLACK, Color.WHITE,
            Color.GRAY, Color.DKGRAY, Color.LTGRAY,
            ContextCompat.getColor(requireContext(), R.color.blue_1b71f4),
            Color.parseColor("#FF5722"), // Deep Orange
            Color.parseColor("#9C27B0"), // Purple
            Color.parseColor("#3F51B5"), // Indigo
            Color.parseColor("#009688"), // Teal
            Color.parseColor("#4CAF50"), // Green
            Color.parseColor("#FFC107"), // Amber
            Color.parseColor("#FF9800"), // Orange
            Color.parseColor("#795548"), // Brown
            Color.parseColor("#607D8B"), // Blue Grey
            Color.parseColor("#E91E63")  // Pink
        )
        
        solidColors.clear()
        // Add custom color picker option
        solidColors.add(
            SolidColorData(
                color = Color.TRANSPARENT,
                isCustomColorPicker = true,
                isSelected = false
            )
        )

        colors.forEach { color ->
            solidColors.add(
                SolidColorData(
                    color = color,
                    isSelected = currentColor.colorPickerType == ColorPickerType.SOLID && currentColor.solidColor == color
                )
            )
        }
    }
    
    private fun setupGradientColors() {
        val gradients = listOf(
            GradientColorData(Color.parseColor("#FF5722"), Color.parseColor("#FFC107"), 0f),
            GradientColorData(Color.parseColor("#9C27B0"), Color.parseColor("#E91E63"), 45f),
            GradientColorData(Color.parseColor("#3F51B5"), Color.parseColor("#2196F3"), 90f),
            GradientColorData(Color.parseColor("#009688"), Color.parseColor("#4CAF50"), 135f),
            GradientColorData(Color.parseColor("#FF9800"), Color.parseColor("#FFEB3B"), 180f),
            GradientColorData(Color.parseColor("#795548"), Color.parseColor("#607D8B"), 225f),
            GradientColorData(Color.parseColor("#E91E63"), Color.parseColor("#9C27B0"), 270f),
            GradientColorData(Color.parseColor("#2196F3"), Color.parseColor("#00BCD4"), 315f)
        ).map { gradient ->
            gradient.copy(
                isSelected = currentColor.colorPickerType == ColorPickerType.GRADIENT &&
                             currentColor.startGradientColor == gradient.startColor &&
                             currentColor.endGradientColor == gradient.endColor &&
                             currentColor.angleGradient == gradient.angle
            )
        }

        gradientColors.clear()
        gradientColors.addAll(gradients)
    }
    
    private fun setupRecyclerViews() {
        // Setup solid colors RecyclerView
        solidColorAdapter = SolidColorAdapter { selectedColor ->
            onSolidColorSelected(selectedColor)
        }
        
        binding.rvSolidColors.apply {
            layoutManager = GridLayoutManager(requireContext(), 7)
            adapter = solidColorAdapter
        }
        
        solidColorAdapter.submitList(solidColors)
        
        // Setup gradient colors RecyclerView
        gradientColorAdapter = GradientColorAdapter { selectedGradient ->
            onGradientColorSelected(selectedGradient)
        }
        
        binding.rvGradientColors.apply {
            layoutManager = GridLayoutManager(requireContext(), 7)
            adapter = gradientColorAdapter
        }
        
        gradientColorAdapter.submitList(gradientColors)

        binding.tvGradientColor.isVisible = supportGradient
        binding.rvGradientColors.isVisible = supportGradient
    }
    
    private fun onSolidColorSelected(selectedColor: SolidColorData) {
        if (selectedColor.isCustomColorPicker) {
            // Open custom color picker bottom sheet
            val customColorPicker = CustomColorPickerBottomSheet.newInstance(
                currentColor = currentColor.solidColor ?: android.graphics.Color.RED,
                title = title
            )
            customColorPicker.setOnColorSelectedListener { colorData ->
                onColorSelected?.invoke(colorData)
            }
            customColorPicker.show(parentFragmentManager, "CustomColorPickerBottomSheet")
            dismiss()
            return
        }
        
        // Update selection state
        solidColors.forEachIndexed { index, color ->
            solidColors[index] = color.copy(isSelected = color.color == selectedColor.color)
        }
        solidColorAdapter.submitList(solidColors.toList())
        
        // Clear gradient selection
        gradientColors.forEachIndexed { index, gradient ->
            gradientColors[index] = gradient.copy(isSelected = false)
        }
        gradientColorAdapter.submitList(gradientColors.toList())
        
        // Notify listener and dismiss
        onColorSelected?.invoke(
            ColorSelectedData(
                colorPickerType = ColorPickerType.SOLID,
                solidColor = selectedColor.color
            )
        )
        dismiss()
    }
    
    private fun onGradientColorSelected(selectedGradient: GradientColorData) {
        // Update selection state
        gradientColors.forEachIndexed { index, gradient ->
            gradientColors[index] = gradient.copy(
                isSelected = gradient.startColor == selectedGradient.startColor && 
                           gradient.endColor == selectedGradient.endColor &&
                           gradient.angle == selectedGradient.angle
            )
        }
        gradientColorAdapter.submitList(gradientColors.toList())
        
        // Clear solid color selection
        solidColors.forEachIndexed { index, color ->
            solidColors[index] = color.copy(isSelected = false)
        }
        solidColorAdapter.submitList(solidColors.toList())
        
        // For gradient, we'll use the start color as the selected color
        // In a real implementation, you might want to handle gradients differently
        onColorSelected?.invoke(
            ColorSelectedData(
                colorPickerType = ColorPickerType.GRADIENT,
                startGradientColor = selectedGradient.startColor,
                endGradientColor = selectedGradient.endColor,
                angleGradient = selectedGradient.angle
            )
        )
        dismiss()
    }
}