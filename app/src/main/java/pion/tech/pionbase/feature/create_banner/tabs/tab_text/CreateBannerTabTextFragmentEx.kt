package pion.tech.pionbase.feature.create_banner.tabs.tab_text

import android.graphics.Color
import android.widget.ImageView
import android.widget.SeekBar
import androidx.core.graphics.toColorInt
import pion.tech.pionbase.R
import pion.tech.pionbase.data.model.create_banner.ColorPickerType
import pion.tech.pionbase.data.model.create_banner.ColorSelectedData
import pion.tech.pionbase.data.model.create_banner.TextStyleData
import pion.tech.pionbase.feature.create_banner.CreateBannerCommonViewModel
import pion.tech.pionbase.feature.create_banner.tabs.tab_text.bottomsheet.ColorPickerBottomSheet
import pion.tech.pionbase.util.collectFlowOnView
import pion.tech.pionbase.util.setPreventDoubleClick
import pion.tech.pionbase.util.setTintColor

fun CreateBannerTabTextFragment.setupTextSizeSeekBar() {
    binding.seekBarTextSize.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                val textSize = CreateBannerCommonViewModel.MIN_TEXT_SIZE +
                    (progress * (CreateBannerCommonViewModel.MAX_TEXT_SIZE - CreateBannerCommonViewModel.MIN_TEXT_SIZE)) / 100
                createBannerCommonViewModel.updateTextSize(textSize)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    })
}

fun CreateBannerTabTextFragment.setupOutlineSeekBar() {
    binding.seekBarOutline.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                createBannerCommonViewModel.updateOutlineWidth(progress)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    })
}

fun CreateBannerTabTextFragment.setupShadowSeekBar() {
    binding.seekBarShadow.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                createBannerCommonViewModel.updateShadowRadius(progress)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    })
}

fun CreateBannerTabTextFragment.showColorPickerBottomSheet(
    currentColor: ColorSelectedData,
    title: String = "",
    supportGradient: Boolean = false,
    onColorSelected: (ColorSelectedData) -> Unit
) {
    val bottomSheet = ColorPickerBottomSheet.newInstance(currentColor, title, supportGradient)
    bottomSheet.setOnColorSelectedListener(onColorSelected)
    bottomSheet.show(parentFragmentManager)
}


fun CreateBannerTabTextFragment.updateStyleTextImageState(
    imageView: ImageView,
    isSelected: Boolean
) {
    imageView.setTintColor(
        if (isSelected) {
            R.color.blue_1b71f4
        } else {
            R.color.gray_85888e
        }
    )
}

fun CreateBannerTabTextFragment.setUpTextColorPicker() {
    binding.viewTextColorPicker.setPreventDoubleClick {
        onTextColorPickerClick()
    }
}

fun CreateBannerTabTextFragment.setUpOutlineColorPicker() {
    binding.viewOutlineColorPicker.setPreventDoubleClick {
        onOutlineColorPickerClick()
    }
}

fun CreateBannerTabTextFragment.setUpShadowColorPicker() {
    binding.viewShadowColorPicker.setPreventDoubleClick {
        onShadowColorPickerClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonClearFormat() {
    binding.btnClearFormat.setPreventDoubleClick {
        onClearFormatClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonBold() {
    binding.btnBold.setPreventDoubleClick {
        onBoldClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonItalic() {
    binding.btnItalic.setPreventDoubleClick {
        onItalicClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonUnderline() {
    binding.btnUnderline.setPreventDoubleClick {
        onUnderlineClick()
    }
}

fun CreateBannerTabTextFragment.observeTextStyleData() {
    createBannerCommonViewModel.textStyleData.collectFlowOnView(viewLifecycleOwner) {
        updateUIFromTextStyleData(it)
    }
}

fun CreateBannerTabTextFragment.updateUIFromTextStyleData(data: TextStyleData) {
    // Update seekbars
    val textSizeProgress = ((data.textSize - CreateBannerCommonViewModel.MIN_TEXT_SIZE) * 100) /
            (CreateBannerCommonViewModel.MAX_TEXT_SIZE - CreateBannerCommonViewModel.MIN_TEXT_SIZE)
    binding.seekBarTextSize.progress = textSizeProgress
    binding.seekBarOutline.progress = data.outlineWidth
    binding.seekBarShadow.progress = data.shadowRadius

    // Update color pickers
    if (data.textColor.colorPickerType == ColorPickerType.SOLID) {
        binding.viewTextColorPicker.setSolidColor(data.textColor.solidColor ?: Color.WHITE)
    } else {
        binding.viewTextColorPicker.setLinearGradient(
            data.textColor.startGradientColor ?: "#FFFFFFFF".toColorInt(),
            data.textColor.endGradientColor ?: "#FF000000".toColorInt(),
            data.textColor.angleGradient ?: 0f
        )
    }

    binding.viewOutlineColorPicker.solidColor = data.outlineColor.toColorInt()
    binding.viewShadowColorPicker.solidColor = data.shadowColor.toColorInt()

    // Update style buttons
    updateStyleTextImageState(binding.btnBold, data.isBold)
    updateStyleTextImageState(binding.btnItalic, data.isItalic)
    updateStyleTextImageState(binding.btnUnderline, data.isUnderline)
}

fun CreateBannerTabTextFragment.onTextColorPickerClick() {
    val currentColor = createBannerCommonViewModel.textStyleData.value.textColor
    showColorPickerBottomSheet(
        currentColor = currentColor,
        title = getString(R.string.text_color),
        supportGradient = true,
    ) { selectedColor ->
        createBannerCommonViewModel.updateTextColor(selectedColor)
    }
}

fun CreateBannerTabTextFragment.onOutlineColorPickerClick() {
    val currentColor = createBannerCommonViewModel.textStyleData.value.outlineColor
    showColorPickerBottomSheet(
        currentColor = ColorSelectedData(
            colorPickerType = ColorPickerType.SOLID,
            solidColor = currentColor.toColorInt()
        ),
        title = getString(R.string.outline_color),
        supportGradient = false,
    ) { selectedColor ->
        createBannerCommonViewModel.updateOutlineColor(String.format("#%08X", selectedColor.solidColor))
    }
}

fun CreateBannerTabTextFragment.onShadowColorPickerClick() {
    val currentColor = createBannerCommonViewModel.textStyleData.value.shadowColor
    showColorPickerBottomSheet(
        currentColor = ColorSelectedData(
            colorPickerType = ColorPickerType.SOLID,
            solidColor = currentColor.toColorInt()
        ),
        title = getString(R.string.shadow_color),
        supportGradient = false,
    ) { selectedColor ->
        createBannerCommonViewModel.updateShadowColor(String.format("#%08X", selectedColor.solidColor))
    }
}

fun CreateBannerTabTextFragment.onClearFormatClick() {
    createBannerCommonViewModel.clearTextFormat()
}

fun CreateBannerTabTextFragment.onBoldClick() {
    createBannerCommonViewModel.toggleTextBold()
}

fun CreateBannerTabTextFragment.onItalicClick() {
    createBannerCommonViewModel.toggleTextItalic()
}

fun CreateBannerTabTextFragment.onUnderlineClick() {
    createBannerCommonViewModel.toggleTextUnderline()
}