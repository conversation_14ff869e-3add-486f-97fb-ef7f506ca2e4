package pion.tech.pionbase.feature.create_banner

import android.graphics.Typeface
import androidx.core.content.ContextCompat
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import pion.tech.pionbase.R
import pion.tech.pionbase.data.model.create_banner.CreateBannerScreenTab
import pion.tech.pionbase.feature.create_banner.CreateBannerFragment.Companion.DEFAULT_DISPLAY_TEXT
import pion.tech.pionbase.feature.create_banner.tabs.tab_text.CreateBannerTabTextFragment

fun CreateBannerFragment.setUpUISelectedTab(tab: CreateBannerScreenTab) {
    resetTabStyles()

    when(tab) {
        CreateBannerScreenTab.TEXT -> {
            // Set text color, background, and typeface for selected tab
            binding.tvTabText.setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_1b71f4))
            binding.tvTabText.setBackgroundResource(R.drawable.bg_tab_selected)
            binding.tvTabText.typeface = Typeface.DEFAULT_BOLD

            //Change to text tab fragment
            replaceFragment(CreateBannerTabTextFragment())
        }

        CreateBannerScreenTab.BACKGROUND -> {
            binding.tvBackground.setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_1b71f4))
            binding.tvBackground.setBackgroundResource(R.drawable.bg_tab_selected)
            binding.tvBackground.typeface = Typeface.DEFAULT_BOLD
        }

        CreateBannerScreenTab.EFFECT -> {
            binding.tvTabEffect.setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_1b71f4))
            binding.tvTabEffect.setBackgroundResource(R.drawable.bg_tab_selected)
            binding.tvTabEffect.typeface = Typeface.DEFAULT_BOLD
        }

        CreateBannerScreenTab.MUSIC -> {
            binding.tvTabMusic.setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_1b71f4))
            binding.tvTabMusic.setBackgroundResource(R.drawable.bg_tab_selected)
            binding.tvTabMusic.typeface = Typeface.DEFAULT_BOLD
        }
    }
}

fun CreateBannerFragment.resetTabStyles() {
    binding.apply {
        val defaultColor = ContextCompat.getColor(requireContext(), R.color.text_secondary)

        tvTabText.setTextColor(defaultColor)
        tvTabText.setBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.transparent))
        tvTabText.typeface = Typeface.DEFAULT

        tvBackground.setTextColor(defaultColor)
        tvBackground.setBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.transparent))
        tvBackground.typeface = Typeface.DEFAULT

        tvTabEffect.setTextColor(defaultColor)
        tvTabEffect.setBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.transparent))
        tvTabEffect.typeface = Typeface.DEFAULT

        tvTabMusic.setTextColor(defaultColor)
        tvTabMusic.setBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.transparent))
        tvTabMusic.typeface = Typeface.DEFAULT
    }
}

fun CreateBannerFragment.replaceFragment(fragment: Fragment) {
    childFragmentManager.beginTransaction()
        .replace(R.id.fragmentContainer, fragment)
        .commit()
}

fun CreateBannerFragment.initView() {
    binding.edtDisplayText.setText(DEFAULT_DISPLAY_TEXT)
}

fun CreateBannerFragment.displayTextChangedEvent() {
    binding.edtDisplayText.doAfterTextChanged { editable ->
        binding.ledView.setText(editable.toString())
    }
}