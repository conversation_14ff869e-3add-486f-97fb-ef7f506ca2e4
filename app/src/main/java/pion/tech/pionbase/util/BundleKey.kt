package pion.tech.pionbase.util

object BundleKey {
    const val KEY_DUMMY_ENTITY = "KEY_DUMMY_ENTITY"
    
    // Text Style Keys
    const val KEY_TEXT_SIZE = "KEY_TEXT_SIZE"
    const val KEY_TEXT_COLOR = "KEY_TEXT_COLOR"
    const val KEY_TEXT_BOLD = "KEY_TEXT_BOLD"
    const val KEY_TEXT_ITALIC = "KEY_TEXT_ITALIC"
    const val KEY_TEXT_UNDERLINE = "KEY_TEXT_UNDERLINE"
    const val KEY_FONT_FAMILY = "KEY_FONT_FAMILY"
    const val KEY_OUTLINE_WIDTH = "KEY_OUTLINE_WIDTH"
    const val KEY_OUTLINE_COLOR = "KEY_OUTLINE_COLOR"
    const val KEY_SHADOW_RADIUS = "KEY_SHADOW_RADIUS"
    const val KEY_SHADOW_COLOR = "KEY_SHADOW_COLOR"
}