package pion.tech.pionbase.util

import android.os.Build

internal object AndroidVersions {
    const val ANDROID_15 = Build.VERSION_CODES.VANILLA_ICE_CREAM // 34
    const val ANDROID_14 = Build.VERSION_CODES.UPSIDE_DOWN_CAKE // 34
    const val ANDROID_13 = Build.VERSION_CODES.TIRAMISU // 33
    const val ANDROID_12 = Build.VERSION_CODES.S // 31
    const val ANDROID_11 = Build.VERSION_CODES.R // 30
    const val ANDROID_10 = Build.VERSION_CODES.Q // 29
    const val ANDROID_9 = Build.VERSION_CODES.P // 28
    const val ANDROID_8 = Build.VERSION_CODES.O // 26
    const val ANDROID_7 = Build.VERSION_CODES.N // 24
    const val ANDROID_6 = Build.VERSION_CODES.M // 23
    const val ANDROID_5 = Build.VERSION_CODES.LOLLIPOP // 21
}
