package pion.tech.pionbase.base.navigator

import android.os.Bundle
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavOptions

class NavigatorImpl(
    private val navController: NavController,
    private val lifecycle: Lifecycle,
    private val currentDestinationId: Int,
) : Navigator {
    private var navObserver: LifecycleEventObserver? = null

    private fun isAtCurrentDestination(): Boolean = navController.currentDestination?.id == currentDestinationId

    private fun safeNav(
        actionId: Int,
        bundle: Bundle? = null,
        navOptions: NavOptions? = null,
    ) {
        if (!isAtCurrentDestination()) return
        runCatching {
            navObserver =
                object : LifecycleEventObserver {
                    override fun onStateChanged(
                        source: LifecycleOwner,
                        event: Lifecycle.Event,
                    ) {
                        if (event == Lifecycle.Event.ON_RESUME) {
                            lifecycle.removeObserver(this)
                            runCatching {
                                if (navController.currentDestination?.id == currentDestinationId) {
                                    navController.navigate(actionId, bundle, navOptions)
                                }
                            }
                        }
                    }
                }
            lifecycle.addObserver(navObserver!!)

            navController.addOnDestinationChangedListener(
                object :
                    NavController.OnDestinationChangedListener {
                    override fun onDestinationChanged(
                        controller: NavController,
                        destination: NavDestination,
                        arguments: Bundle?,
                    ) {
                        if (destination.id != currentDestinationId) {
                            navController.removeOnDestinationChangedListener(this)
                            lifecycle.removeObserver(navObserver as LifecycleEventObserver)
                        }
                    }
                },
            )

            if (navController.currentDestination?.id == currentDestinationId) {
                navController.navigate(actionId, bundle, navOptions)
            }
        }
    }

    override fun getCurrentDestinationId(): Int = navController.currentDestination?.id ?: 0

    override fun navigateTo(
        actionId: Int,
        bundle: Bundle?,
    ) {
        safeNav(actionId, bundle)
    }

    override fun navigateTo(
        actionId: Int,
        bundle: Bundle?,
        clearBackStack: Boolean,
    ) {
        val navOptions =
            if (clearBackStack) {
                NavOptions
                    .Builder()
                    .setPopUpTo(navController.graph.startDestinationId, true)
                    .build()
            } else {
                null
            }
        safeNav(actionId, bundle, navOptions)
    }

    private fun safeNavigateUp() {
        if (!isAtCurrentDestination()) return
        runCatching {
            navObserver =
                object : LifecycleEventObserver {
                    override fun onStateChanged(
                        source: LifecycleOwner,
                        event: Lifecycle.Event,
                    ) {
                        if (event == Lifecycle.Event.ON_RESUME) {
                            lifecycle.removeObserver(this)
                            runCatching {
                                if (navController.currentDestination?.id == currentDestinationId) {
                                    navController.navigateUp()
                                }
                            }
                        }
                    }
                }
            lifecycle.addObserver(navObserver!!)

            navController.addOnDestinationChangedListener(
                object :
                    NavController.OnDestinationChangedListener {
                    override fun onDestinationChanged(
                        controller: NavController,
                        destination: NavDestination,
                        arguments: Bundle?,
                    ) {
                        if (destination.id != currentDestinationId) {
                            navController.removeOnDestinationChangedListener(this)
                            lifecycle.removeObserver(navObserver as LifecycleEventObserver)
                        }
                    }
                },
            )

            if (navController.currentDestination?.id == currentDestinationId) {
                navController.navigateUp()
            }
        }
    }

    private fun safePopBackStack(
        destinationId: Int,
        inclusive: Boolean,
    ) {
        if (!isAtCurrentDestination()) return
        runCatching {
            navObserver =
                object : LifecycleEventObserver {
                    override fun onStateChanged(
                        source: LifecycleOwner,
                        event: Lifecycle.Event,
                    ) {
                        if (event == Lifecycle.Event.ON_RESUME) {
                            lifecycle.removeObserver(this)
                            runCatching {
                                if (navController.currentDestination?.id == currentDestinationId) {
                                    navController.popBackStack(destinationId, inclusive)
                                }
                            }
                        }
                    }
                }
            lifecycle.addObserver(navObserver!!)

            navController.addOnDestinationChangedListener(
                object :
                    NavController.OnDestinationChangedListener {
                    override fun onDestinationChanged(
                        controller: NavController,
                        destination: NavDestination,
                        arguments: Bundle?,
                    ) {
                        if (destination.id != currentDestinationId) {
                            navController.removeOnDestinationChangedListener(this)
                            lifecycle.removeObserver(navObserver as LifecycleEventObserver)
                        }
                    }
                },
            )

            runCatching {
                if (navController.currentDestination?.id == currentDestinationId) {
                    navController.popBackStack(destinationId, inclusive)
                }
            }
        }
    }

    override fun navigateUp() {
        safeNavigateUp()
    }

    override fun addOnDestinationChangedListener(listener: (NavController, NavDestination?, Bundle?) -> Unit) {
        navController.addOnDestinationChangedListener(listener)
    }

    override fun isCameFrom(destinationId: Int): Boolean = navController.previousBackStackEntry?.destination?.id == destinationId

    override fun popBackStack(
        destinationId: Int,
        inclusive: Boolean,
    ) {
        safePopBackStack(destinationId, inclusive)
    }
}
