<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_main.xml"
    app:startDestination="@id/splashFragment">

    <fragment
        android:id="@+id/splashFragment"
        android:name="pion.tech.pionbase.feature.splash.SplashFragment"
        android:label="SplashFragment"
        tools:layout="@layout/fragment_splash">
        <action
            android:id="@+id/action_splashFragment_to_languageFragment"
            app:destination="@id/languageFragment"
            app:popUpTo="@id/splashFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_splashFragment_to_homeFragment"
            app:destination="@id/homeFragment"
            app:popUpTo="@id/homeFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_splashFragment_to_createBannerFragment"
            app:destination="@id/createBannerFragment" />
    </fragment>
    <fragment
        android:id="@+id/homeFragment"
        android:name="pion.tech.pionbase.feature.home.HomeFragment"
        android:label="HomeFragment"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_homeFragment_to_settingFragment"
            app:destination="@id/settingFragment" />
    </fragment>
    <fragment
        android:id="@+id/languageFragment"
        android:name="pion.tech.pionbase.feature.language.LanguageFragment"
        android:label="LanguageFragment"
        tools:layout="@layout/fragment_language">
        <action
            android:id="@+id/action_languageFragment_to_onboardFragment"
            app:destination="@id/onboardFragment"
            app:popUpTo="@id/languageFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_languageFragment_to_splashFragment"
            app:destination="@id/splashFragment" />
    </fragment>
    <fragment
        android:id="@+id/onboardFragment"
        android:name="pion.tech.pionbase.feature.onboard.OnboardFragment"
        android:label="OnboardFragment"
        tools:layout="@layout/fragment_onboard">
        <action
            android:id="@+id/action_onboardFragment_to_homeFragment"
            app:destination="@id/homeFragment"
            app:popUpTo="@id/onboardFragment"
            app:popUpToInclusive="true" />
    </fragment>
    <fragment
        android:id="@+id/settingFragment"
        android:name="pion.tech.pionbase.feature.setting.SettingFragment"
        android:label="SettingFragment"
        tools:layout="@layout/fragment_setting">
        <action
            android:id="@+id/action_settingFragment_to_languageFragment"
            app:destination="@id/languageFragment" />
    </fragment>

    <fragment
        android:id="@+id/createBannerFragment"
        android:name="pion.tech.pionbase.feature.create_banner.CreateBannerFragment"
        android:label="CreateBannerFragment"
        tools:layout="@layout/fragment_create_banner" />

</navigation>