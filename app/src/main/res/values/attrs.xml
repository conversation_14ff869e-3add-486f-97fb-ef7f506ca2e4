<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="cornerRadius" format="dimension" />
    <attr name="backgroundColor" format="color" />
    <attr name="strokeWidth" format="dimension" />
    <attr name="strokeColor" format="color" />
    
    <declare-styleable name="LEDView">
        <attr name="led_text" format="string" />
        <attr name="led_textSize" format="dimension" />
        <attr name="led_textColor" format="color" />
        <attr name="led_isBold" format="boolean" />
        <attr name="led_isItalic" format="boolean" />
        <attr name="led_isUnderline" format="boolean" />
        <attr name="led_fontFamily" format="string" />
        <attr name="led_outlineWidth" format="dimension" />
        <attr name="led_outlineColor" format="color" />
        <attr name="led_shadowRadius" format="dimension" />
        <attr name="led_shadowColor" format="color" />
        <attr name="led_animationSpeed" format="integer" />
        <attr name="led_dotRadius" format="dimension" />
        <attr name="led_dotSpacing" format="dimension" />
        <attr name="led_backgroundColor" format="color" />
    </declare-styleable>
</resources>