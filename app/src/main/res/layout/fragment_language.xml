<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:backgroundTint="#E38007"
        android:orientation="horizontal"
        app:layout_constraintDimensionRatio="360:72"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="@dimen/_32dp"
            android:layout_height="@dimen/_32dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/_16dp"
            android:contentDescription="@null"
            android:padding="@dimen/_6dp"
            android:src="@drawable/ic_back"
            android:visibility="gone" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/_24dp"
            android:layout_marginEnd="@dimen/_8dp"
            android:layout_weight="1"
            android:fontFamily="@font/font_600"
            android:text="@string/language"
            android:textColor="#727272"
            android:textSize="@dimen/_26sp" />

        <TextView
            android:id="@+id/ivDone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/_20sp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/_24dp"
            android:padding="@dimen/_6dp"
            android:text="Ok"
            android:textAllCaps="true"
            android:visibility="gone" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_main"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/_12dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintBottom_toTopOf="@+id/layoutAds"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:spanCount="3"
        tools:listitem="@layout/item_language" />

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#C7C7C7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black" />

        </FrameLayout>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>