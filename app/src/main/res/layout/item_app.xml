<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_12dp">

        <ImageView
            android:id="@+id/ivAppIcon"
            android:layout_width="@dimen/_48dp"
            android:layout_height="@dimen/_48dp"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@android:drawable/sym_def_app_icon" />

        <TextView
            android:id="@+id/tvAppName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_12dp"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_16sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivAppIcon"
            app:layout_constraintTop_toTopOf="@+id/ivAppIcon"
            tools:text="App Name" />

        <TextView
            android:id="@+id/tvPackageName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_4dp"
            android:layout_marginEnd="@dimen/_12dp"
            android:textColor="@android:color/darker_gray"
            android:textSize="@dimen/_12sp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivAppIcon"
            app:layout_constraintTop_toBottomOf="@+id/tvAppName"
            tools:text="com.example.app" />

        <TextView
            android:id="@+id/tvVersionName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginTop="@dimen/_2dp"
            android:layout_marginEnd="@dimen/_12dp"
            android:textColor="@android:color/darker_gray"
            android:textSize="@dimen/_10sp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivAppIcon"
            app:layout_constraintTop_toBottomOf="@+id/tvPackageName"
            app:layout_constraintBottom_toBottomOf="@+id/ivAppIcon"
            tools:text="1.0.0" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>