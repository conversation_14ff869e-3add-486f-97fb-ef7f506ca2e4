<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/_35dp"
        android:layout_margin="@dimen/_2dp">

        <View
            android:id="@+id/viewColorBackground"
            android:layout_width="@dimen/_28dp"
            android:layout_height="@dimen/_28dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:backgroundTint="@color/red" />

        <!-- Selection Border -->
        <View
            android:id="@+id/viewSelectionBorder"
            android:layout_width="@dimen/_35dp"
            android:layout_height="@dimen/_35dp"
            android:background="@drawable/bg_gradient_picker_selected"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <!-- Color Wheel Icon for custom color picker -->
        <ImageView
            android:id="@+id/ivColorWheel"
            android:layout_width="@dimen/_28dp"
            android:layout_height="@dimen/_28dp"
            android:src="@drawable/ic_color_picker"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/viewColorBackground"
            app:layout_constraintEnd_toEndOf="@id/viewColorBackground"
            app:layout_constraintStart_toStartOf="@id/viewColorBackground"
            app:layout_constraintTop_toTopOf="@id/viewColorBackground"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
