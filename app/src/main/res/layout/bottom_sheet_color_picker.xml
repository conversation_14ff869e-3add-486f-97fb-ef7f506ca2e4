<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_bottom_sheet_rounded">

        <!-- Header -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingVertical="@dimen/_12dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_600"
                android:gravity="center"
                android:text="@string/text_color"
                android:maxWidth="@dimen/_230dp"
                android:textColor="@color/white"
                android:textSize="@dimen/_18sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/close"
                android:src="@drawable/baseline_close_24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <!-- Content -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fillViewport="true"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingBottom="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/layoutHeader">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Solid Color Section -->
                <TextView
                    android:id="@+id/tvSolidColor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/solid_color"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16sp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvSolidColors"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12dp"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    tools:itemCount="12"
                    tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    tools:listitem="@layout/item_color_picker"
                    tools:spanCount="6" />

                <!-- Gradient Color Section -->
                <TextView
                    android:id="@+id/tvGradientColor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_24dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/gradient_color"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16sp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvGradientColors"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12dp"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    tools:itemCount="14"
                    tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    tools:listitem="@layout/item_gradient_picker"
                    tools:spanCount="7" />

            </LinearLayout>

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>