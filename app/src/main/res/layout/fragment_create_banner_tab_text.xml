<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_0c111d"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_16dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutTextSize"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            android:paddingVertical="@dimen/_8dp">

            <ImageView
                android:id="@+id/ivTextSize"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:src="@drawable/ic_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Text Size Section -->
            <TextView
                android:id="@+id/tvTextSize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_size"
                android:textColor="@color/white_cecfd2"
                android:textSize="@dimen/_14sp"
                android:layout_marginStart="@dimen/_4dp"
                android:fontFamily="@font/font_500"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTextSize"
                app:layout_constraintTop_toTopOf="parent" />

            <SeekBar
                android:id="@+id/seekBarTextSize"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:max="100"
                android:progress="50"
                app:layout_constraintDimensionRatio="V,98:14"
                android:progressTint="@color/slider_thumb"
                android:thumbTint="@color/slider_thumb"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutTextColor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintTop_toBottomOf="@id/layoutTextSize">

            <ImageView
                android:id="@+id/ivTextColor"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:src="@drawable/ic_text_color"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Text Size Section -->
            <TextView
                android:id="@+id/tvTextColor"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_color"
                android:textColor="@color/white_cecfd2"
                android:textSize="@dimen/_14sp"
                android:layout_marginStart="@dimen/_4dp"
                android:fontFamily="@font/font_500"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTextColor"
                app:layout_constraintTop_toTopOf="parent" />

            <pion.tech.pionbase.custom_view.ColorDisplayView
                android:id="@+id/viewTextColorPicker"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:background="@drawable/bg_radius_8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clTextStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintTop_toBottomOf="@id/layoutTextColor">

            <ImageView
                android:id="@+id/ivTextStyle"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:src="@drawable/ic_text_style"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Text Size Section -->
            <TextView
                android:id="@+id/tvTextStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_style"
                android:textColor="@color/white_cecfd2"
                android:textSize="@dimen/_14sp"
                android:layout_marginStart="@dimen/_4dp"
                android:fontFamily="@font/font_500"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTextStyle"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutTextStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvTextStyle"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/btnClearFormat"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:contentDescription="@string/clear_format"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_none_text"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/btnBold"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/btnBold"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:src="@drawable/ic_bold_text"
                    android:contentDescription="@string/bold"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/btnItalic"
                    android:textColor="@color/dark_text_primary" />

                <ImageView
                    android:id="@+id/btnItalic"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:src="@drawable/ic_italic_text"
                    android:contentDescription="@string/italic"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/btnUnderline" />

                <ImageView
                    android:id="@+id/btnUnderline"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:src="@drawable/ic_underline_text"
                    android:contentDescription="@string/underline"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutTextFontStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintTop_toBottomOf="@id/clTextStyle">

            <ImageView
                android:id="@+id/ivFontSelection"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:src="@drawable/ic_font_select"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Text Size Section -->
            <TextView
                android:id="@+id/tvFontSelection"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/font"
                android:textColor="@color/white_cecfd2"
                android:textSize="@dimen/_14sp"
                android:layout_marginStart="@dimen/_4dp"
                android:fontFamily="@font/font_500"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivFontSelection"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvSelectedFont"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_4dp"
                tools:text="Arial"
                android:background="@drawable/bg_radius_8"
                android:padding="@dimen/_5dp"
                android:backgroundTint="@color/black_333741"
                android:textColor="@color/white_cecfd2"
                android:textSize="@dimen/_14sp"
                android:fontFamily="@font/font_500"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutTextOutline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintTop_toBottomOf="@id/layoutTextFontStyle">

            <ImageView
                android:id="@+id/ivTextOutline"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:src="@drawable/ic_text_outline"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Text Size Section -->
            <TextView
                android:id="@+id/tvTextOutline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/outline"
                android:textColor="@color/white_cecfd2"
                android:textSize="@dimen/_14sp"
                android:layout_marginStart="@dimen/_4dp"
                android:fontFamily="@font/font_500"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTextOutline"
                app:layout_constraintTop_toTopOf="parent" />

            <SeekBar
                android:id="@+id/seekBarOutline"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:max="10"
                android:progress="0"
                android:progressTint="@color/slider_thumb"
                android:thumbTint="@color/slider_thumb"
                app:layout_constraintDimensionRatio="V,98:14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutTextOutlineColor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintTop_toBottomOf="@id/layoutTextOutline">

            <ImageView
                android:id="@+id/ivTextOutlineColor"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:src="@drawable/ic_outline_text_color"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Text Size Section -->
            <TextView
                android:id="@+id/tvTextOutlineColor"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/outline_color"
                android:textColor="@color/white_cecfd2"
                android:textSize="@dimen/_14sp"
                android:layout_marginStart="@dimen/_4dp"
                android:fontFamily="@font/font_500"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTextOutlineColor"
                app:layout_constraintTop_toTopOf="parent" />

            <pion.tech.pionbase.custom_view.ColorDisplayView
                android:id="@+id/viewOutlineColorPicker"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:background="@drawable/bg_radius_8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutTextShadow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintTop_toBottomOf="@id/layoutTextOutlineColor">

            <ImageView
                android:id="@+id/ivTextShadow"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:src="@drawable/ic_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Text Size Section -->
            <TextView
                android:id="@+id/tvTextShadow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/shadow"
                android:textColor="@color/white_cecfd2"
                android:textSize="@dimen/_14sp"
                android:layout_marginStart="@dimen/_4dp"
                android:fontFamily="@font/font_500"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTextShadow"
                app:layout_constraintTop_toTopOf="parent" />

            <SeekBar
                android:id="@+id/seekBarShadow"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:max="10"
                android:progress="0"
                android:progressTint="@color/slider_thumb"
                android:thumbTint="@color/slider_thumb"
                app:layout_constraintDimensionRatio="V,98:14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutTextShadowColor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            app:layout_constraintTop_toBottomOf="@id/layoutTextShadow">

            <ImageView
                android:id="@+id/ivTextShadowColor"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:src="@drawable/ic_shadow_color"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Text Size Section -->
            <TextView
                android:id="@+id/tvTextShadowColor"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/shadow_color"
                android:textColor="@color/white_cecfd2"
                android:textSize="@dimen/_14sp"
                android:layout_marginStart="@dimen/_4dp"
                android:fontFamily="@font/font_500"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTextShadowColor"
                app:layout_constraintTop_toTopOf="parent" />

            <pion.tech.pionbase.custom_view.ColorDisplayView
                android:id="@+id/viewShadowColorPicker"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:background="@drawable/bg_radius_8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>