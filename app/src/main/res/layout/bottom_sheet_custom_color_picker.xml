<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_bottom_sheet_rounded">

        <!-- Header -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingVertical="@dimen/_12dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_600"
                android:gravity="center"
                android:text="@string/text_color"
                android:maxWidth="@dimen/_230dp"
                android:textColor="@color/white"
                android:textSize="@dimen/_18sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/close"
                android:src="@drawable/baseline_close_24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingBottom="@dimen/_24dp"
            app:layout_constraintTop_toBottomOf="@id/layoutHeader">

            <!-- Color Picker -->
            <com.skydoves.colorpickerview.ColorPickerView
                android:id="@+id/colorPickerView"
                android:layout_width="@dimen/_280dp"
                android:layout_height="@dimen/_280dp"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_24dp"
                android:layout_marginBottom="@dimen/_32dp"
                app:selector="@drawable/wheel"
                app:preferenceName="MyColorPicker" />

            <!-- Done Button -->
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnDone"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_48dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_button_primary"
                android:fontFamily="@font/font_600"
                android:text="@string/done"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_16sp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>