<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="#4D000000">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:backgroundTint="#000000"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="@dimen/_25dp"
            android:theme="@style/RoundedCorner.16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/spinKit"
                android:layout_width="@dimen/_100dp"
                android:layout_height="@dimen/_80dp"
                android:layout_gravity="center_horizontal"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/loading_dialog" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/please_wait"
                android:textColor="#FFFFFF" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>