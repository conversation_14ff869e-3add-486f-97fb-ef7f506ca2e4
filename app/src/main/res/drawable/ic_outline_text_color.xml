<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M19.865,2C21.042,2 22,2.958 22,4.135V19.865C22,21.042 21.042,22 19.865,22H4.135C2.958,22 2,21.042 2,19.865V4.135C2,2.958 2.958,2 4.135,2H19.865ZM4.135,3.666C3.877,3.666 3.666,3.877 3.666,4.135V19.865C3.666,20.123 3.877,20.334 4.135,20.334H19.865C20.123,20.334 20.334,20.123 20.334,19.865V4.135C20.334,3.877 20.123,3.666 19.865,3.666H4.135ZM17.506,4.952C18.365,4.952 19.063,5.651 19.065,6.51V8.57C19.064,9.429 18.366,10.129 17.507,10.129H14.49V17.666C14.49,18.525 13.792,19.224 12.933,19.224H10.871C10.012,19.224 9.314,18.525 9.313,17.666V10.129H6.297C5.438,10.129 4.739,9.43 4.738,8.571V6.51C4.738,5.651 5.438,4.952 6.297,4.952H17.506ZM6.405,6.618V8.462H9.421C10.28,8.462 10.979,9.161 10.979,10.021V17.557H12.823V10.021C12.823,9.161 13.522,8.462 14.381,8.462H17.397V6.618H6.405Z">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="12"
          android:startY="22"
          android:endX="12"
          android:endY="2"
          android:type="linear">
        <item android:offset="0" android:color="#FF5F9CF8"/>
        <item android:offset="1" android:color="#FF0564F4"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
