<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M12,18C16.418,18 20,14.418 20,10C20,5.582 16.418,2 12,2C7.582,2 4,5.582 4,10C4,14.418 7.582,18 12,18Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient
          android:startX="12"
          android:startY="18"
          android:endX="12"
          android:endY="2"
          android:type="linear">
        <item android:offset="0" android:color="#FF5F9CF8"/>
        <item android:offset="1" android:color="#FF0564F4"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6,22H18"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient
          android:startX="12"
          android:startY="23"
          android:endX="12"
          android:endY="22"
          android:type="linear">
        <item android:offset="0" android:color="#FF5F9CF8"/>
        <item android:offset="1" android:color="#FF0564F4"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
