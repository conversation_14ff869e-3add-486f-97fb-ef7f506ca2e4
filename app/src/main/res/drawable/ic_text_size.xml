<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M6,12H18M6,20V4M18,20V4"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient
          android:startX="12"
          android:startY="20"
          android:endX="12"
          android:endY="4"
          android:type="linear">
        <item android:offset="0" android:color="#FF5F9CF8"/>
        <item android:offset="1" android:color="#FF0564F4"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
