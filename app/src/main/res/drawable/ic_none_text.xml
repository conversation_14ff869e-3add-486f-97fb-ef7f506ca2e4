<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="16dp"
    android:height="16dp"
    android:viewportWidth="16"
    android:viewportHeight="16">
  <group>
    <clip-path
        android:pathData="M0,0h16v16h-16z"/>
    <path
        android:pathData="M14.667,1.333L1.333,14.667M14.667,8C14.667,11.682 11.682,14.667 8,14.667C4.318,14.667 1.333,11.682 1.333,8C1.333,4.318 4.318,1.333 8,1.333C11.682,1.333 14.667,4.318 14.667,8Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.3"
        android:fillColor="#00000000"
        android:strokeLineCap="round">
      <aapt:attr name="android:strokeColor">
        <gradient
            android:startX="8"
            android:startY="14.667"
            android:endX="8"
            android:endY="1.333"
            android:type="linear">
          <item android:offset="0" android:color="#FF5F9CF8"/>
          <item android:offset="1" android:color="#FF0564F4"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
