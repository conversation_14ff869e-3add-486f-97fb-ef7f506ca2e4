<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M21.93,12.86C21.91,13.05 21.88,13.23 21.83,13.41C20.79,12.53 19.44,12 17.97,12C14.66,12 11.97,14.69 11.97,18C11.97,19.47 12.5,20.82 13.38,21.86C13.2,21.91 13.02,21.94 12.83,21.96C11.98,22.04 11.11,22 10.21,21.85C6.1,21.15 2.79,17.82 2.11,13.7C0.98,6.85 6.82,1.01 13.67,2.14C17.79,2.82 21.12,6.13 21.82,10.24C21.97,11.14 22.01,12.01 21.93,12.86Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient
          android:startX="11.97"
          android:startY="22"
          android:endX="11.97"
          android:endY="1.999"
          android:type="linear">
        <item android:offset="0" android:color="#FF5F9CF8"/>
        <item android:offset="1" android:color="#FF0564F4"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M21.83,13.41C21.69,13.9 21.43,14.34 21.06,14.71L14.68,21.09C14.31,21.46 13.87,21.72 13.38,21.86C12.5,20.82 11.97,19.47 11.97,18C11.97,14.69 14.66,12 17.97,12C19.44,12 20.79,12.53 21.83,13.41Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient
          android:startX="16.9"
          android:startY="21.86"
          android:endX="16.9"
          android:endY="12"
          android:type="linear">
        <item android:offset="0" android:color="#FF5F9CF8"/>
        <item android:offset="1" android:color="#FF0564F4"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
