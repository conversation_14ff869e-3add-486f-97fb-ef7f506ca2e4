{"formatVersion": 1, "database": {"version": 1, "identityHash": "e3056daf6ff764a2e6aeb7c714441f66", "entities": [{"tableName": "dummy", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `desc` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "desc", "columnName": "desc", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e3056daf6ff764a2e6aeb7c714441f66')"]}}