{"formatVersion": 1, "database": {"version": 1, "identityHash": "167050a779eb1d6cb622501e06b8b507", "entities": [{"tableName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER NOT NULL, `VALUE` TEXT NOT NULL, PRIMARY KEY(`ID`))", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "value", "columnName": "VALUE", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["ID"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '167050a779eb1d6cb622501e06b8b507')"]}}