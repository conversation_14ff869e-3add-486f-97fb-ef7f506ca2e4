<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adViewHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/ads_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/ad_app_icon_container"
            android:layout_width="@dimen/_40dp"
            android:layout_height="@dimen/_40dp"
            android:layout_marginTop="@dimen/_13dp"
            app:cardCornerRadius="@dimen/_8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/ad_app_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/ad_headline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_10dp"
            android:layout_marginTop="@dimen/_4dp"
            android:ellipsize="end"
            android:fontFamily="@font/font_600"
            android:gravity="center_horizontal"
            android:maxLines="1"
            android:textColor="@color/content_text_color"
            android:textSize="@dimen/_16sp"
            app:layout_constraintTop_toBottomOf="@+id/ad_app_icon_container"
            tools:text="Duck Adventure App" />

        <TextView
            android:id="@+id/ad_body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_10dp"
            android:layout_marginTop="@dimen/_4dp"
            android:ellipsize="end"
            android:fontFamily="@font/font_400"
            android:gravity="center_horizontal"
            android:maxLines="1"
            android:textColor="@color/content_text_color"
            android:textSize="@dimen/_12sp"
            app:layout_constraintTop_toBottomOf="@+id/ad_headline"
            tools:text="Mô tả ngắn" />

        <TextView
            android:id="@+id/ad_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_10dp"
            android:layout_marginTop="@dimen/_4dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/cta_color"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/cta_text_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="340:42"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ad_body"
            tools:text="OPEN" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_ad_tag"
            android:backgroundTint="#FFA800"
            android:fontFamily="@font/font_500"
            android:paddingHorizontal="@dimen/_4dp"
            android:paddingVertical="@dimen/_1dp"
            android:text="@string/ad_attribution"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>