<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adViewHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/ads_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/ad_app_icon_container"
            android:layout_width="@dimen/_56dp"
            android:layout_height="@dimen/_56dp"
            android:layout_marginStart="@dimen/_13dp"
            android:layout_marginTop="@dimen/_10dp"
            app:cardCornerRadius="@dimen/_8dp"
            app:layout_constraintBottom_toTopOf="@id/ad_call_to_action"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ad_call_to_action"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/ad_app_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_26dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@+id/ad_app_icon_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ad_app_icon_container"
            app:layout_constraintTop_toTopOf="@+id/ad_app_icon_container">

            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_16sp"
                tools:text="Duck Adventure App" />

            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lineSpacingExtra="@dimen/_4dp"
                android:maxLines="2"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_10sp"
                tools:text="Đây là mô tả của đoạn quảng cáo này có thể xuống tới 2 dòng như thế này, Đây là mô tả của đoạn quảng cáo này có thể x" />

        </LinearLayout>

        <TextView
            android:id="@+id/ad_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_13dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_marginBottom="@dimen/_7dp"
            android:background="@drawable/bg_radius_20"
            android:backgroundTint="@color/cta_color"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:textColor="@color/cta_text_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="341:42"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ad_app_icon_container"
            tools:text="OPEN" />

        <TextView
            android:id="@+id/txv_ads"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_ad_tag"
            android:backgroundTint="#FFA800"
            android:fontFamily="@font/font_500"
            android:paddingHorizontal="@dimen/_4dp"
            android:paddingVertical="@dimen/_1dp"
            android:text="@string/ad_attribution"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>