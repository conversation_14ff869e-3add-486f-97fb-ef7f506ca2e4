<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/bg_out_ads"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:125"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" >

        <androidx.cardview.widget.CardView
            android:id="@+id/adViewParent"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="332:105"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="vertical"
            app:cardElevation="0dp"
            app:cardCornerRadius="@dimen/_8dp"
            android:background="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/adViewHolder"
                android:background="@color/white"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/linearLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toTopOf="@+id/ad_call_to_action"
                    app:layout_constraintEnd_toEndOf="@+id/ad_call_to_action"
                    app:layout_constraintStart_toStartOf="@+id/ad_call_to_action"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/ad_headline"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fontFamily="@font/font_700"
                        android:maxLines="1"
                        android:textColor="@color/content_text_color"
                        android:textSize="@dimen/_14sp"
                        tools:text="Duck Adventure App" />

                    <TextView
                        android:id="@+id/ad_body"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fontFamily="@font/font_400"
                        android:maxLines="1"
                        android:textColor="@color/content_text_color"
                        android:textSize="@dimen/_12sp"
                        tools:text="Mô tả ngắn" />


                </LinearLayout>

                <TextView
                    android:id="@+id/ad_call_to_action"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginBottom="@dimen/_13dp"
                    android:backgroundTint="#CC1C83E2"
                    android:maxLines="1"
                    android:gravity="center"
                    tools:text="OPEN"
                    android:textColor="@color/white"
                    app:layout_constraintDimensionRatio="272:30"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:background="@drawable/bg_radius_8"
                    android:layout_marginHorizontal="@dimen/_14dp"
                    app:layout_constraintStart_toStartOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_radius_1"
                    android:backgroundTint="#FFA800"
                    android:fontFamily="@font/font_500"
                    android:paddingHorizontal="@dimen/_4dp"
                    android:paddingVertical="@dimen/_1dp"
                    android:text="@string/ad_attribution"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_12sp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_radius_8_stroke"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
    


</androidx.constraintlayout.widget.ConstraintLayout>