<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/bg_ads">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adViewHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/ads_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_19dp"
            android:layout_marginTop="@dimen/_20dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ad_call_to_action"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/font_600"
                android:maxLines="1"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_16sp"
                tools:text="Duck Adventure App" />

            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:ellipsize="end"
                android:fontFamily="@font/font_400"
                android:maxLines="1"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_12sp"
                tools:text="Mô tả ngắn" />

        </LinearLayout>

        <TextView
            android:id="@+id/ad_call_to_action"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/_18dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/cta_color"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/cta_text_color"
            app:layout_constraintBottom_toBottomOf="@+id/linearLayout"
            app:layout_constraintDimensionRatio="100:40"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/linearLayout"
            app:layout_constraintWidth_percent="0.278"
            tools:text="OPEN" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_ad_tag"
            android:backgroundTint="#FFA800"
            android:fontFamily="@font/font_500"
            android:paddingHorizontal="@dimen/_4dp"
            android:paddingVertical="@dimen/_1dp"
            android:text="@string/ad_attribution"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>