<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="#4D000000"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingBottom="20dp"
        android:background="@drawable/bg_radius_12"
        android:backgroundTint="@color/white"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintWidth_percent="0.85"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imvAds"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:src="@drawable/ic_phone_ads"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="26dp" />

        <TextView
            android:id="@+id/txvAds"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/loading_ads"
            android:textColor="@color/black"
            android:layout_gravity="center_horizontal"
            android:textSize="16sp"
            android:layout_marginTop="16dp"
            android:layout_marginHorizontal="16dp"/>

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/spinKit"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="15dp"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/loading" />

    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>