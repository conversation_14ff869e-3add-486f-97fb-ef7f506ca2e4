<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adViewHolder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/ads_background"
        app:layout_constraintDimensionRatio="360:162"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/ad_app_icon_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/_21dp"
            app:cardCornerRadius="@dimen/_8dp"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintHeight_percent="0.25"
            app:layout_constraintStart_toStartOf="@+id/ad_call_to_action"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/ad_app_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_26dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@+id/ad_call_to_action"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/ad_app_icon_container"
            app:layout_constraintTop_toBottomOf="@+id/ad_app_icon_container">

            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/font_600"
                android:maxLines="1"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_16sp"
                tools:text="Duck Adventure App" />

            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/font_400"
                android:maxLines="1"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_16sp"
                tools:text="Mô tả ngắn" />

        </LinearLayout>

        <TextView
            android:id="@+id/ad_call_to_action"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/_12dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/cta_color"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/cta_text_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="130:30"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintWidth_percent="0.36"
            tools:text="OPEN" />

        <FrameLayout
            android:id="@+id/ad_media"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/ad_call_to_action"
            app:layout_constraintEnd_toStartOf="@+id/ad_call_to_action"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ad_app_icon_container"
            app:layout_constraintWidth_percent="0.564" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_radius_1"
            android:backgroundTint="#FFA800"
            android:fontFamily="@font/font_500"
            android:paddingHorizontal="@dimen/_4dp"
            android:paddingVertical="@dimen/_1dp"
            android:text="@string/ad_attribution"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>