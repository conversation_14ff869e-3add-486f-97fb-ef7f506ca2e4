<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adViewHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/ads_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <FrameLayout
            android:id="@+id/ad_media_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_10dp"
            android:layout_marginTop="@dimen/_10dp"
            app:layout_constraintDimensionRatio="340:142"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:id="@+id/ad_media"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cardCornerRadius="@dimen/_8dp"
                app:cardElevation="0dp" />

        </FrameLayout>

        <LinearLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#80000000"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/_8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:id="@+id/ad_app_icon_container"
                android:layout_width="@dimen/_36dp"
                android:layout_height="@dimen/_36dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/_10dp"
                app:cardCornerRadius="@dimen/_8dp">

                <FrameLayout
                    android:id="@+id/ad_app_icon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:id="@+id/linearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@id/ad_app_icon_container"
                app:layout_constraintEnd_toStartOf="@+id/textView2"
                app:layout_constraintStart_toEndOf="@+id/ad_app_icon_container"
                app:layout_constraintTop_toTopOf="@id/ad_app_icon_container">

                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="@font/font_600"
                    android:maxLines="1"
                    android:textColor="@color/content_text_color"
                    android:textSize="@dimen/_16sp"
                    tools:text="Duck Adventure App" />

                <TextView
                    android:id="@+id/ad_body"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="@font/font_400"
                    android:maxLines="1"
                    android:textColor="@color/content_text_color"
                    android:textSize="@dimen/_10sp"
                    tools:text="Mô tả ngắn" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/ad_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_10dp"
            android:layout_marginTop="@dimen/_6dp"
            android:layout_marginBottom="@dimen/_7dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/cta_color"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/cta_text_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="340:32"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ad_media_container"
            tools:text="OPEN" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_ad_tag"
            android:backgroundTint="#FFA800"
            android:fontFamily="@font/font_500"
            android:paddingHorizontal="@dimen/_4dp"
            android:paddingVertical="@dimen/_1dp"
            android:text="@string/ad_attribution"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>