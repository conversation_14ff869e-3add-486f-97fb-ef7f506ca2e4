<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:tag="collapsible"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/adViewParent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="360:159"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/adViewHolder"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/ads_background">

            <androidx.cardview.widget.CardView
                android:id="@+id/ad_app_icon"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:cardCornerRadius="@dimen/_8dp"
                android:layout_marginTop="@dimen/_12dp"
                android:layout_marginBottom="@dimen/_6dp"
                app:layout_constraintBottom_toTopOf="@+id/head_container"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/head_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginBottom="@dimen/_6dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                app:layout_constraintBottom_toTopOf="@+id/ad_body" >

                <TextView
                    android:id="@+id/txv_ads"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_radius_1"
                    android:backgroundTint="#FFA800"
                    android:layout_gravity="center_vertical"
                    android:text="@string/ad_attribution"
                    android:textColor="@color/white"
                    android:fontFamily="@font/font_500"
                    android:paddingHorizontal="@dimen/_2dp"/>

                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textColor="@color/content_text_color"
                    android:fontFamily="@font/font_600"
                    android:maxLines="1"
                    android:textSize="@dimen/_16sp"
                    android:ellipsize="end"
                    android:layout_marginStart="@dimen/_6dp"
                    tools:text="Duck Adventure App Duck Adventure App Duck Adventure App Duck Adventure App"/>


            </LinearLayout>

            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_6dp"
                android:ellipsize="end"
                android:fontFamily="@font/font_500"
                android:maxLines="1"
                android:text="Đây là mô tả của đoạn quảng Đây là mô tả của đoạn quảng Đây là mô tả của đoạn quảng"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_12sp"
                app:layout_constraintBottom_toTopOf="@+id/ad_call_to_action"/>


            <TextView
                android:id="@+id/ad_call_to_action"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_11dp"
                android:background="@drawable/bg_radius_10"
                android:backgroundTint="@color/cta_color"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:text="OPEN"
                android:textColor="@color/cta_text_color"
                android:textSize="@dimen/_12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="331:38"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>