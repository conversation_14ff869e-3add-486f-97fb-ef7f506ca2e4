<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adViewHolder"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/ad_media"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <androidx.cardview.widget.CardView
            android:id="@+id/ad_app_icon_container"
            android:layout_width="@dimen/_56dp"
            android:layout_height="@dimen/_56dp"
            android:layout_marginStart="@dimen/_8dp"
            app:cardCornerRadius="@dimen/_8dp"
            android:layout_marginBottom="@dimen/_13dp"
            app:layout_constraintBottom_toTopOf="@+id/ad_call_to_action"
            app:layout_constraintStart_toStartOf="parent">

            <FrameLayout
                android:id="@+id/ad_app_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_4dp"
            android:layout_marginEnd="@dimen/_9dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@+id/ad_app_icon_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ad_app_icon_container"
            app:layout_constraintTop_toTopOf="@id/ad_app_icon_container">

            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/font_600"
                android:maxLines="1"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_16sp"
                tools:text="Duck Adventure App" />

            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/font_400"
                android:maxLines="2"
                android:lineSpacingExtra="@dimen/_4dp"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_12sp"
                tools:text="Đây là mô tả của đoạn quảng cáo này có ây là mô tả của đoạn quảng cáo này có" />

        </LinearLayout>

        <TextView
            android:id="@+id/ad_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_8dp"
            android:layout_marginBottom="@dimen/_36dp"
            android:background="@drawable/bg_radius_5"
            android:backgroundTint="#CC1C83E2"
            android:textAlignment="center"
            android:gravity="center"
            android:fontFamily="@font/font_700"
            app:layout_constraintDimensionRatio="312:30"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="OPEN" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_radius_1"
            android:backgroundTint="#FFA800"
            android:fontFamily="@font/font_600"
            android:paddingHorizontal="@dimen/_4dp"
            android:paddingVertical="@dimen/_1dp"
            android:text="@string/advertisement"
            android:textAllCaps="true"
            android:textColor="@color/black"
            android:textSize="@dimen/_12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
    


</androidx.constraintlayout.widget.ConstraintLayout>