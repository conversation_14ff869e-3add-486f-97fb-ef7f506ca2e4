plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'pion.datlt.libads'
    compileSdk 34

    defaultConfig {
        minSdk 24


        versionCode 3
        versionName "1.0.2"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        viewBinding = true
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.13.1'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.navigation:navigation-runtime-ktx:2.7.7'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    // Auto dimen
    def auto_dimen_version = "1.0.8"
    implementation "com.github.hantrungkien:AutoDimension:$auto_dimen_version"

    def glide_version = "4.12.0"
    implementation "com.github.bumptech.glide:glide:$glide_version"
    annotationProcessor "com.github.bumptech.glide:compiler:$glide_version"

    implementation 'com.google.code.gson:gson:2.10'
    implementation 'com.airbnb.android:lottie:5.2.0'

    //ads
    implementation 'com.google.android.gms:play-services-ads:23.1.0'
    //gdpr
    implementation("com.google.android.ump:user-messaging-platform:2.2.0")

    //appflyer mmp
    implementation 'com.appsflyer:af-android-sdk:6.15.0'
    implementation "com.android.installreferrer:installreferrer:2.2"


    implementation  'com.google.ads.mediation:ironsource:8.7.0.0'
    implementation  'com.google.ads.mediation:vungle:7.4.3.0'
    implementation  'com.google.ads.mediation:facebook:6.18.0.0'
    implementation  'com.google.ads.mediation:mintegral:16.9.41.0'
    implementation  'com.google.ads.mediation:pangle:6.5.0.3.0'
    implementation  'com.unity3d.ads:unity-ads:4.13.1'
    implementation  'com.google.ads.mediation:unity:4.13.1.0'





}