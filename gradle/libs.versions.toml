[versions]
autodimension = "1.0.8"
cardview = "1.0.0"
core = "3.3.0"
datastorePreferences = "1.1.2"
kotlinStdlib = "2.0.21"
legacySupportV4 = "1.0.0"
chuckerLibrary = "4.1.0"
lib_recovery = "1.0.1"
recovery = "1.0.0"
retrofit = "2.11.0"
roundablelayout = "1.1.4"
roundedimageview = "2.3.0"
compileSdkVersion = "35"
minSdkVersion = "24"
targetSdkVersion = "35"

agp = "8.7.3"
kotlin = "2.0.20"
ksp = "2.0.20-1.0.25"
coreKtx = "1.15.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
material = "1.12.0"
activity = "1.9.3"
constraintlayout = "2.2.0"
recyclerview = "1.3.2"
swiperefreshlayout = "1.1.0"
navigation = "2.8.5"
hilt = "2.52"
room = "2.6.1"
coroutines = "1.9.0"
lifecycleExtensions = "2.2.0"
lifecycle = "2.8.7"
lottie = "6.5.2"
glide = "4.16.0"
firebase-bom = "33.7.0"
googleServices = "4.4.2"
firebaseCrashlytics = "3.0.2"
firebasePerf = "1.4.2"
converterGson = "2.11.0"
okhttp-bom = "4.12.0"
timber = "5.0.1"
media3 = "1.5.0"
viewpager2 = "1.1.0"
leakcanary = "2.14"
firebaseConfigKtx = "22.1.0"

[libraries]
androidx-appcompat-resources = { module = "androidx.appcompat:appcompat-resources", version.ref = "appcompat" }
androidx-cardview = { module = "androidx.cardview:cardview", version.ref = "cardview" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastorePreferences" }
androidx-legacy-support-v4 = { module = "androidx.legacy:legacy-support-v4", version.ref = "legacySupportV4" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycle" }
androidx-viewpager2 = { module = "androidx.viewpager2:viewpager2", version.ref = "viewpager2" }
autodimension = { module = "com.github.hantrungkien:AutoDimension", version.ref = "autodimension" }
bottomsheets = { module = "com.afollestad.material-dialogs:bottomsheets", version.ref = "core" }
core = { module = "com.afollestad.material-dialogs:core", version.ref = "core" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib", version.ref = "kotlinStdlib" }
chucker-library = { module = "com.github.chuckerteam.chucker:library", version.ref = "chuckerLibrary" }
chucker-library-no-op = { module = "com.github.chuckerteam.chucker:library-no-op", version.ref = "chuckerLibrary" }
lib_recovery = { module = "com.github.khaisao:lib_recovery", version.ref = "lib_recovery" }
lifecycle = { module = "com.afollestad.material-dialogs:lifecycle", version.ref = "core" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
androidx-swiperefreshlayout = { module = "androidx.swiperefreshlayout:swiperefreshlayout", version.ref = "swiperefreshlayout" }
hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hilt" }
hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "hilt" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "coroutines" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }
androidx-lifecycle-extensions = { module = "androidx.lifecycle:lifecycle-extensions", version.ref = "lifecycleExtensions" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigation" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navigation" }
androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navigation" }
lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebase-bom" }
firebase-crashlytics-ktx = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
firebase-analytics-ktx = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
firebase-perf-ktx = { group = "com.google.firebase", name = "firebase-perf-ktx" }
firebase-config-ktx = { group = "com.google.firebase", name = "firebase-config-ktx" }
firebase-messaging-ktx = { group = "com.google.firebase", name = "firebase-messaging-ktx" }
converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "converterGson" }
okhttp-bom = { module = "com.squareup.okhttp3:okhttp-bom", version.ref = "okhttp-bom" }
okhttp3-okhttp = { group = "com.squareup.okhttp3", name = "okhttp" }
okhttp3-logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor" }
recovery = { module = "com.zxy.android:recovery", version.ref = "recovery" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
roundablelayout = { module = "com.github.zladnrms:RoundableLayout", version.ref = "roundablelayout" }
roundedimageview = { module = "com.makeramen:roundedimageview", version.ref = "roundedimageview" }
timber = { module = "com.jakewharton.timber:timber", version.ref = "timber" }
androidx-media3-ui = { module = "androidx.media3:media3-ui", version.ref = "media3" }
androidx-media3-common = { module = "androidx.media3:media3-common", version.ref = "media3" }
androidx-media3-session = { module = "androidx.media3:media3-session", version.ref = "media3" }
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3" }
androidx-media3-exoplayer-hls = { module = "androidx.media3:media3-exoplayer-hls", version.ref = "media3" }
androidx-media3-exoplayer-dash = { module = "androidx.media3:media3-exoplayer-dash", version.ref = "media3" }
androidx-media3-exoplayer-smoothstreaming = { module = "androidx.media3:media3-exoplayer-smoothstreaming", version.ref = "media3" }
androidx-media3-exoplayer-rtsp = { module = "androidx.media3:media3-exoplayer-rtsp", version.ref = "media3" }
google-firebase-config-ktx = { group = "com.google.firebase", name = "firebase-config-ktx", version.ref = "firebaseConfigKtx" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
google-devtools-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
androidx-navigation-safeargs-kotlin = { id = "androidx.navigation.safeargs.kotlin", version.ref = "navigation" }
dagger-hilt-android = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
gms-google-services = { id = "com.google.gms.google-services", version.ref = "googleServices" }
google-firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlytics" }
google-firebase-perf = { id = "com.google.firebase.firebase-perf", version.ref = "firebasePerf" }

